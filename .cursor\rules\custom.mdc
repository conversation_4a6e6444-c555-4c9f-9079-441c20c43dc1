---
alwaysApply: true
---

1、所有的信息提示都需要使用 `plugins/modal.js` 中的合适的方法，不要使用 `uni.showToast` 等方法。
2、所有的路由跳转都是用 `plugins/tab.js` 中的合适的方法，不要使用 `uni.navigateTo` 等方法。
3、需要使用 uni-app 的组件时，请使用 `uni_modules` 中的组件，并且不需要导入就可以使用。
4、在创建全局通用组件时，需要在 `components` 目录下创建，使用大驼峰命名，并且需要使用 `setup` 语法。
5、在创建局部组件时，需要在当前页面目录下的 `components` 目录下创建，使用小驼峰命名，并且需要使用 `setup` 语法。
6、在创建一个业务模块时，需要思考是否可以抽离出通用组件，如果可以，则抽离出通用组件，否则在当前页面目录下创建。
7、在创建一个业务模块时，需要思考是否可以抽离出通用方法，如果可以，则抽离出通用方法，否则在当前页面目录下创建。
8、变量使用 const 关键字定义，方法中的变量使用 let 关键字定义，方法使用 function 关键字定义，不要使用箭头函数。
9、在判断时，使用 `==` 而不是 `===` 。
10、所有列表空数据时，使用 `EmptyData` 组件，不要使用 `uni-empty` 组件。
