module.exports = {
  // 每行最大字符数，超出会自动换行
  printWidth: 120,
  // 使用制表符而不是空格缩进
  useTabs: false,
  // 缩进大小
  tabWidth: 2,
  // 语句末尾是否使用分号
  semi: false,
  // 使用单引号
  singleQuote: true,
  // 对象属性是否需要引号
  quoteProps: 'as-needed',
  // JSX中使用单引号
  jsxSingleQuote: false,
  // 多行时尾随逗号
  trailingComma: 'es5',
  // 对象字面量中括号之间的空格
  bracketSpacing: true,
  // 将多行HTML元素的>放在最后一行的末尾，而不是单独放在下一行
  bracketSameLine: false,
  // 箭头函数参数在只有一个参数时是否加括号
  arrowParens: 'avoid',
  // 格式化文件的起始位置
  rangeStart: 0,
  rangeEnd: Infinity,
  // 是否格式化嵌入在文件中的引用代码
  embeddedLanguageFormatting: 'auto',
  // Vue文件中<script>和<style>标签内的代码是否缩进
  vueIndentScriptAndStyle: false,
  // 换行符使用lf
  endOfLine: 'lf',
  // HTML空白敏感度
  htmlWhitespaceSensitivity: 'css',
} 