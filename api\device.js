import request from '@/utils/request'

// 获取结构列表
export function pointCategory (params) {
  return request({
    url: '/category',
    method: 'get',
    params,
  })
}

// 获取结构下的点位
export function pointList (params) {
  return request({
    url: '/getPointId',
    method: 'get',
    params,
  })
}

// 获取设备列表
export function deviceList (params) {
  return request({
    url: '/deviceList',
    method: 'post',
    data: params,
  })
}

// 获取设备详情
export function deviceDetail (params) {
  return request({
    url: '/deviceDetail',
    method: 'get',
    params,
  })
}

// 维保记录
export function maintenanceRecord (params) {
  return request({
    url: '/getMaintenDetail',
    method: 'get',
    params,
  })
}

// 获取第二级设备类型
export function sonDeviceType (params) {
  return request({
    url: '/getSonDeviceType',
    method: 'get',
    params,
  })
}

// 获取设备名称列表
export function getDeviceNameList (params) {
  return request({
    url: '/getDeviceName',
    method: 'get',
    params,
  })
}

// 添加维保记录
export function addMainten (params) {
  return request({
    url: '/addMainten',
    method: 'post',
    data: params,
  })
}




