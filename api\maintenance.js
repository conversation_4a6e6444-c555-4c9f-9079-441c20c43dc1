import request from "@/utils/request";

// 获取维保记录
export function getMaintenanceRecords(params) {
  return request({
    url: '/main-list',
    method: 'post',
    data: params,
  })
}

// 获取维保记录详情
export function getMaintenanceRecordDetail(params) {
  return request({
    url: '/main-baseInfo',
    method: 'get',
    params,
  })
}

// 添加维保记录
export function addMaintenanceRecords(params) {
  return request({
    url: '/main-add',
    method: 'post',
    data: params,
  })
}

// 删除草稿
export function deleteDraft(params) {
  return request({
    url: '/main-draft-del',
    method: 'get',
    params,
  })
}

// 添加备注
export function addRemarks(params) {
  return request({
    url: '/main-remark',
    method: 'get',
    params,
  })
}

// 作废维保记录
export function cancelMaintenanceRecord(params) {
  return request({
    url: '/main-cancel',
    method: 'get',
    params,
  })
}

// 单个设备处理
export function processSingleDevice(params) {
  return request({
    url: '/device-process',
    method: 'post',
    data: params,
  })
}

// 提交处理结果
export function submitProcessResult(params) {
  return request({
    url: '/main-device-process-add',
    method: 'post',
    data: params,
  })
}
