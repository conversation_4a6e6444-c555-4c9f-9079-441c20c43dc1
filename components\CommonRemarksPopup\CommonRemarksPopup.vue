<template>
  <!-- 通用备注弹窗组件 -->
  <uni-popup ref="popup" type="bottom" :safe-area="false" :mask-click="false">
    <view class="remarks-popup-container">
      <view class="remarks-header">
        <text class="cancel-btn" @tap="handleCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm-btn" @tap="handleSubmit">{{ buttonText }}</text>
      </view>
      <view class="remarks-content">
        <textarea
          class="remarks-textarea"
          v-model="remarks"
          :placeholder="placeholder"
          placeholder-style="color: #999; font-size: 32rpx;"
          :maxlength="maxLength"
          :auto-height="false"
          :show-confirm-bar="false"
          :cursor-spacing="100"
          :adjust-position="true"
          :fixed="true"
          :focus="autoFocus"
        />
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, defineProps, defineEmits, defineExpose, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: '填写备注',
  },
  // 输入框占位文本
  placeholder: {
    type: String,
    default: '请输入备注信息',
  },
  // 最大字符数
  maxLength: {
    type: Number,
    default: 500,
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: '提交',
  },
  // 初始备注内容
  value: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['cancel', 'submit', 'success', 'error'])

// 内部状态
const popup = ref(null)
const remarks = ref(props.value)
const autoFocus = ref(false)

// 存储当前操作的相关数据
const currentOptions = ref({
  id: '',
  submitFunc: null, // 存储外部传入的提交函数
  extraData: {}, // 额外的数据
})

// 打开弹窗
const open = (options = {}) => {
  console.log('弹窗打开方法被调用', options)

  // 重置状态
  remarks.value = options.value || props.value || ''

  // 保存提交函数和相关数据
  currentOptions.value = {
    id: options.id || '',
    submitFunc: options.submitFunc || null, // 存储外部传入的提交函数
    extraData: options.extraData || {}, // 额外的数据
    ...options,
  }

  // 确保popup引用存在
  if (!popup.value) {
    console.error('popup引用不存在')
    return
  }

  // 打开弹窗
  popup.value.open('bottom')

  // 延迟聚焦，确保弹窗完全打开后再聚焦
  setTimeout(() => {
    autoFocus.value = true
  }, 300)
}

// 关闭弹窗
const close = () => {
  autoFocus.value = false
  popup.value.close()
}

// 取消操作
const handleCancel = () => {
  close()
  emit('cancel')
}

// 提交备注
const handleSubmit = async () => {
  if (!remarks.value.trim()) {
    return
  }

  // 触发提交事件
  emit('submit', remarks.value)

  // 如果设置了提交函数，则调用该函数
  const { submitFunc, id, extraData } = currentOptions.value
  if (typeof submitFunc === 'function') {
    try {
      proxy.$modal.loading('提交中...')

      // 构建请求参数
      const requestData = {
        id,
        remark: remarks.value,
        ...extraData,
      }

      // 调用提交函数
      const res = await submitFunc(requestData)

      // 显示成功提示

      proxy.$modal.msgSuccess('提交成功')

      // 触发成功事件
      emit('success', res)

      // 关闭弹窗
      setTimeout(() => {
        close()
      }, 300)
    } catch (error) {
      console.error('提交备注失败:', error)

      // 显示错误提示
      proxy.$modal.msgError(error.message || '提交失败，请重试')

      // 触发错误事件
      emit('error', error)
    } finally {
      proxy.$modal.closeLoading()
    }
  } else {
    console.warn('未提供提交函数，仅触发提交事件')
    // 如果没有设置提交函数，则直接关闭弹窗
    close()
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.remarks-popup-container {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.remarks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.cancel-btn {
  font-size: 32rpx;
  color: #999;
}

.confirm-btn {
  font-size: 32rpx;
  color: #2979ff;
}

.remarks-content {
  padding: 30rpx;
}

.remarks-textarea {
  width: 100%;
  height: 400rpx; /* 提供更大的输入区域 */
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
</style>
