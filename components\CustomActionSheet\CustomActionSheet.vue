<template>
  <view class="custom-action-sheet-container">
    <view class="custom-action-sheet" :class="{ 'show': modelValue }">
      <view class="custom-action-sheet-mask" @tap="handleCancel" :class="{ 'show': modelValue }"></view>
      <view class="custom-action-sheet-content" :class="{ 'show': modelValue }">
        <view class="custom-action-sheet-header" v-if="title">
          <text class="custom-action-sheet-title">{{ title }}</text>
        </view>
        <scroll-view scroll-y class="custom-action-sheet-list" :style="{ maxHeight: maxHeight + 'rpx' }">
          <view
            class="custom-action-sheet-item"
            v-for="(item, index) in options"
            :key="index"
            @tap="handleSelect(item)"
            :class="{ 'active': isItemSelected(item) }"
          >
            <text class="custom-action-sheet-item-text">{{ getItemText(item) }}</text>
          </view>
        </scroll-view>
        <view class="custom-action-sheet-footer">
          <view class="custom-action-sheet-cancel" @tap="handleCancel">
            <text>{{ cancelText }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, watch, ref } from 'vue';

const props = defineProps({
  // 控制显示
  modelValue: {
    type: Boolean,
    default: false
  },
  // 选项列表
  options: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 取消按钮文字
  cancelText: {
    type: String,
    default: '取消'
  },
  // 显示文本的字段名
  textField: {
    type: String,
    default: 'label'
  },
  // 绑定ID的字段名
  valueField: {
    type: String,
    default: 'value'
  },
  // 当前选中的值
  value: {
    type: [String, Number, Array],
    default: null
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 最大高度
  maxHeight: {
    type: Number,
    default: 600
  }
});

const emit = defineEmits(['update:modelValue', 'update:value', 'select', 'cancel']);

// 获取选项显示的文本
const getItemText = (item) => {
  if (typeof item === 'string') {
    return item;
  }
  return item[props.textField] || '';
};

// 获取选项的值
const getItemValue = (item) => {
  if (typeof item === 'string') {
    return item;
  }
  return item[props.valueField];
};

// 判断选项是否被选中
const isItemSelected = (item) => {
  const itemValue = getItemValue(item);
  if (props.multiple && Array.isArray(props.value)) {
    return props.value.includes(itemValue);
  }
  return props.value === itemValue;
};

// 处理选择
const handleSelect = (item) => {
  const itemValue = getItemValue(item);
  
  // 更新选中值（双向绑定）
  emit('update:value', itemValue);
  
  // 触发选择事件
  emit('select', item, itemValue);
  
  // 如果不是多选，选择后自动关闭
  if (!props.multiple) {
    emit('update:modelValue', false);
  }
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
  emit('update:modelValue', false);
};
</script>

<style lang="scss" scoped>
.custom-action-sheet-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
}

.custom-action-sheet {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  
  &.show {
    visibility: visible;
  }
  
  &-mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: auto;
    
    &.show {
      opacity: 1;
    }
  }
  
  &-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f8f8f8;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    pointer-events: auto;
    
    &.show {
      transform: translateY(0);
    }
  }
  
  &-header {
    padding: 20rpx 30rpx;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  
  &-title {
    font-size: 28rpx;
    color: #999;
  }
  
  &-list {
    max-height: 600rpx;
  }
  
  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    
    &.active {
      background-color: #f2f2f2;
      
      .custom-action-sheet-item-text {
        color: #007aff;
      }
    }
    
    &-text {
      font-size: 32rpx;
      color: #333;
    }
  }
  
  &-footer {
    padding: 20rpx 0;
  }
  
  &-cancel {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    background-color: #fff;
    margin-top: 12rpx;
    
    text {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
  }
}
</style> 