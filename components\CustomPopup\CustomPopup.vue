<template>
  <uni-popup ref="popup" type="bottom" background-color="#fff" @change="change">
    <view class="custom-popup">
      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">{{ title }}</text>
        <view class="popup-close" @tap="close">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 内容区域 - 使用插槽让用户自定义 -->
      <view class="popup-content">
        <slot></slot>
      </view>

      <!-- 底部按钮区域 -->
      <view class="popup-footer" v-if="showFooter">
        <button class="btn btn-reset" @tap="handleReset" v-if="showReset">{{ resetText }}</button>
        <button class="btn btn-confirm" @tap="handleConfirm">{{ confirmText }}</button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch } from 'vue'

// 定义props
const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: '筛选条件',
  },
  // 是否显示底部按钮区域
  showFooter: {
    type: Boolean,
    default: true,
  },
  // 是否显示重置按钮
  showReset: {
    type: Boolean,
    default: true,
  },
  // 确认按钮文字
  confirmText: {
    type: String,
    default: '确定',
  },
  // 重置按钮文字
  resetText: {
    type: String,
    default: '重置筛选条件',
  },
  // 控制弹窗显示
  modelValue: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm', 'reset', 'close'])

// 弹窗引用
const popup = ref(null)

// 监听modelValue变化
watch(
  () => props.modelValue,
  val => {
    if (val) {
      open()
    } else {
      close()
    }
  }
)

// 打开弹窗
const open = () => {
  popup.value.open()
}

// 关闭弹窗
const close = () => {
  popup.value.close()
}

// 监听弹窗状态变化
const change = e => {
  emit('update:modelValue', e.show)
}

// 确认按钮点击事件
const handleConfirm = () => {
  emit('confirm')
  close()
}

// 重置按钮点击事件
const handleReset = () => {
  emit('reset')
}

// 向外暴露方法
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.custom-popup {
  padding-bottom: env(safe-area-inset-bottom);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 100rpx;
  border-bottom: 1px solid #f5f5f5;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-icon {
  font-size: 48rpx;
  color: #999;
  font-weight: 100;
}

.popup-content {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.popup-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1px solid #f5f5f5;
}

.btn {
  height: 90rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  margin: 0;
}

.btn-reset {
  flex: 1;
  background-color: #fff;
  color: #2979ff;
  margin-right: 20rpx;
  border: 1px solid #2979ff;
}

.btn-confirm {
  flex: 1;
  background-color: #2979ff;
  color: #fff;
}
</style>
