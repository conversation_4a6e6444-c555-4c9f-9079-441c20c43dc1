<template>
  <uni-swipe-action>
    <uni-swipe-action-item :right-options="swipeOptions" @click="handleSwipeClick" :disabled="!enableSwipe">
      <!-- 自定义滑动操作插槽 -->
      <template #right>
        <slot name="swipeActions"></slot>
      </template>

      <view class="card-container" @tap="handleCardClick">
        <!-- 卡片内容插槽 -->
        <slot name="content"></slot>
      </view>
    </uni-swipe-action-item>
  </uni-swipe-action>
</template>

<script setup>
import { computed } from 'vue'

// 定义props
const props = defineProps({
  // 是否启用滑动操作
  enableSwipe: {
    type: Boolean,
    default: true,
  },
  // 自定义滑动操作选项
  customSwipeOptions: {
    type: Array,
    default: () => [],
  },
  // 当前卡片数据
  currentData: {
    type: Object,
    default: () => {},
  },
})

// 定义事件
const emit = defineEmits(['swipe-click', 'card-click'])

// 默认滑动操作选项
const defaultSwipeOptions = [
  {
    text: '编辑',
    style: {
      backgroundColor: '#007aff',
    },
  },
  {
    text: '删除',
    style: {
      backgroundColor: '#dd524d',
    },
  },
]

// 计算滑动操作选项
const swipeOptions = computed(() => {
  return props.customSwipeOptions.length > 0 ? props.customSwipeOptions : defaultSwipeOptions
})

// 处理滑动操作点击
const handleSwipeClick = e => {
  emit('swipe-click', {
    index: e.index,
    option: swipeOptions.value[e.index],
    currentData: props.currentData,
  })
}

// 处理卡片点击
const handleCardClick = () => {
  emit('card-click')
}
</script>

<style lang="scss" scoped>
.card-container {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
</style>
