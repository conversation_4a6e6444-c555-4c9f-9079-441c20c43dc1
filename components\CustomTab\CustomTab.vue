<template>
  <view class="custom-tab">
    <!-- Tab选项卡区域 -->
    <view class="tab-container" :style="containerStyle">
      <view class="tab-wrapper" :class="[`tab-wrapper--${styleType}`]">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="[
            `tab-item--${styleType}`,
            { 'tab-item--active': currentIndex === index },
            { 'tab-item--disabled': isTabDisabled(tab, index) },
          ]"
          @tap="handleTabClick(index)"
        >
          <text class="tab-text" :class="{ 'tab-text--active': currentIndex === index }">
            {{ getTabName(tab) }}
          </text>
          <!-- 指示条 - 仅在text模式下显示 -->
          <view
            v-if="styleType === 'text'"
            class="tab-indicator"
            :class="{ 'tab-indicator--active': currentIndex === index }"
          ></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

/**
 * CustomTab - 基于uni-segmented-control的全局可复用Tab组件
 * @description 封装uni-segmented-control，提供更灵活的Tab功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 定义props
const props = defineProps({
  // Tab选项数组，支持字符串数组或对象数组
  tabs: {
    type: Array,
    default: () => [],
    validator: value => {
      return Array.isArray(value) && value.length > 0
    },
  },
  // 当前选中的tab索引，支持双向绑定
  modelValue: {
    type: Number,
    default: 0,
  },
  // 分段器样式类型：button(按钮类型) | text(文字类型)
  styleType: {
    type: String,
    default: 'button',
    validator: value => ['button', 'text'].includes(value),
  },
  // 选中状态的颜色
  activeColor: {
    type: String,
    default: '#2979FF',
  },
  // 未选中状态的颜色
  inActiveColor: {
    type: String,
    default: 'transparent',
  },
  // 容器自定义样式
  containerStyle: {
    type: [String, Object],
    default: '',
  },
  // 是否禁用某些tab项
  disabled: {
    type: Array,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'change', 'click'])

// 响应式数据
const currentIndex = ref(props.modelValue)

// 计算属性 - 处理tab名称数组
const tabNames = computed(() => {
  return props.tabs.map(tab => {
    if (typeof tab === 'string') {
      return tab
    } else if (typeof tab === 'object' && tab.name) {
      return tab.name
    } else if (typeof tab === 'object' && tab.label) {
      return tab.label
    }
    return String(tab)
  })
})

// 计算属性 - 当前选中的tab对象
const currentTab = computed(() => {
  return props.tabs[currentIndex.value] || null
})

// 监听modelValue变化
watch(
  () => props.modelValue,
  newVal => {
    if (newVal !== currentIndex.value && newVal >= 0 && newVal < props.tabs.length) {
      currentIndex.value = newVal
    }
  },
  { immediate: true }
)

// 监听tabs数组变化，确保currentIndex在有效范围内
watch(
  () => props.tabs,
  newTabs => {
    if (currentIndex.value >= newTabs.length) {
      currentIndex.value = Math.max(0, newTabs.length - 1)
      emit('update:modelValue', currentIndex.value)
    }
  },
  { deep: true }
)

/**
 * 获取tab名称
 * @param {String|Object} tab - tab数据
 * @returns {String} tab名称
 */
const getTabName = tab => {
  if (typeof tab === 'string') {
    return tab
  } else if (typeof tab === 'object' && tab.name) {
    return tab.name
  } else if (typeof tab === 'object' && tab.label) {
    return tab.label
  }
  return String(tab)
}

/**
 * 检查tab是否被禁用
 * @param {String|Object} tab - tab数据
 * @param {Number} index - tab索引
 * @returns {Boolean} 是否禁用
 */
const isTabDisabled = (tab, index) => {
  // 检查是否在禁用数组中
  if (props.disabled.includes(index)) {
    return true
  }

  // 检查tab对象本身是否有disabled属性
  if (typeof tab === 'object' && tab.disabled) {
    return true
  }

  return false
}

/**
 * 处理tab点击事件
 * @param {Number} index - 点击的tab索引
 */
const handleTabClick = index => {
  // 检查是否禁用
  if (isTabDisabled(props.tabs[index], index)) {
    return
  }

  // 如果索引没有变化，不执行后续操作
  if (index === currentIndex.value) {
    return
  }

  const oldIndex = currentIndex.value
  currentIndex.value = index

  // 触发双向绑定更新
  emit('update:modelValue', index)

  // 触发change事件，提供更多信息
  emit('change', {
    currentIndex: index,
    oldIndex: oldIndex,
    currentTab: props.tabs[index],
    oldTab: props.tabs[oldIndex],
  })

  // 触发click事件
  emit('click', {
    index: index,
    tab: props.tabs[index],
  })
}

/**
 * 手动切换到指定tab
 * @param {Number} index - 目标tab索引
 */
const switchTo = index => {
  if (index >= 0 && index < props.tabs.length && !props.disabled.includes(index)) {
    handleTabClick(index)
  }
}

/**
 * 获取当前选中的tab信息
 * @returns {Object} 当前tab信息
 */
const getCurrentTab = () => {
  return {
    index: currentIndex.value,
    tab: currentTab.value,
  }
}

// 向外暴露方法
defineExpose({
  switchTo,
  getCurrentTab,
  currentIndex: computed(() => currentIndex.value),
  currentTab,
})
</script>

<style lang="scss" scoped>
.custom-tab {
  width: 100%;
}

.tab-container {
  width: 100%;
  background-color: #fff;
}

/* Tab包装器 */
.tab-wrapper {
  width: 100%;
  display: flex;
  position: relative;

  /* button模式样式 */
  &.tab-wrapper--button {
    background-color: #f5f5f5;
    border-radius: 12rpx;
    padding: 6rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  /* text模式样式 */
  &.tab-wrapper--text {
    background-color: transparent;
    border-bottom: 2rpx solid #f0f0f0;
  }
}

/* Tab项 */
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  /* button模式样式 */
  &.tab-item--button {
    height: 80rpx;
    border-radius: 8rpx;
    background-color: transparent;

    &.tab-item--active {
      background-color: #fff;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  /* text模式样式 */
  &.tab-item--text {
    height: 88rpx;
    background-color: transparent;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 4rpx;
      background-color: #2979ff;
      transition: width 0.3s ease;
      border-radius: 2rpx;
    }

    &.tab-item--active {
      &::after {
        width: 60rpx;
      }
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  /* 禁用状态 */
  &.tab-item--disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:active {
      transform: none;
      background-color: transparent;
    }
  }
}

/* Tab文字 */
.tab-text {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  transition: color 0.3s ease;
  color: #666;

  /* button模式下的文字颜色 */
  .tab-item--button & {
    &.tab-text--active {
      color: #2979ff;
      font-weight: 600;
    }
  }

  /* text模式下的文字颜色 */
  .tab-item--text & {
    &.tab-text--active {
      color: #2979ff;
      font-weight: 600;
    }
  }
}

/* 指示条 - 仅text模式 */
.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 4rpx;
  background-color: #2979ff;
  border-radius: 2rpx;
  transition: all 0.3s ease;
  opacity: 0;
  width: 0;

  &.tab-indicator--active {
    opacity: 1;
    width: 60rpx; /* 固定宽度，只在文字下方 */
  }
}

/* 移动端优化 */
@media screen and (max-width: 750px) {
  .tab-text {
    font-size: 28rpx;
  }

  .tab-item--button {
    height: 72rpx;
  }

  .tab-item--text {
    height: 80rpx;
  }
}

/* 触摸反馈优化 */
.tab-item {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}
</style>
