<template>
  <view class="empty-container">
    <icon :type="iconType" :size="iconSize" :color="iconColor" />
    <text class="empty-text">{{ text }}</text>
  </view>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  // 图标类型
  iconType: {
    type: String,
    default: 'info',
  },
  // 图标大小
  iconSize: {
    type: [Number, String],
    default: 48,
  },
  // 图标颜色
  iconColor: {
    type: String,
    default: '#999',
  },
  // 提示文本
  text: {
    type: String,
    default: '暂无数据',
  },
})
</script>

<style lang="scss" scoped>
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;

  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin-top: 20rpx;
  }
}
</style>
