<template>
  <CustomPopup
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="选择具体位置"
    @confirm="handleConfirm"
    @reset="handleReset"
  >
    <!-- 结构选择 -->
    <view class="structure-container">
      <view class="structure-title">选择结构</view>
      <scroll-view class="structure-list" scroll-y="true" style="height: 200rpx">
        <view
          class="structure-item"
          v-for="structure in structures"
          :key="structure[structureValueField]"
          :class="{ active: selectedStructureId == structure[structureValueField] }"
          @tap="selectStructure(structure)"
        >
          <view class="structure-info">
            <text class="structure-name">{{ structure[structureLabelField] }}</text>
          </view>
          <view class="structure-check" v-if="selectedStructureId == structure[structureValueField]">
            <uni-icons type="checkmarkempty" size="16" color="#007aff"></uni-icons>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="structures.length === 0" class="empty-state">
          <EmptyData text="暂无结构数据" />
        </view>
      </scroll-view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input
          v-model="searchKeyword"
          class="search-input"
          placeholder="搜索点位名称"
          placeholder-style="color: #999;"
        />
        <view v-if="searchKeyword" class="search-clear" @tap="clearSearch">
          <uni-icons type="clear" size="14" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 点位列表 -->
    <view class="location-container">
      <view class="location-title">
        {{ searchKeyword ? `搜索结果 (${filteredPoints.length})` : '选择点位' }}
      </view>
      <scroll-view class="location-list" scroll-y="true" style="height: 300rpx">
        <view
          class="location-item"
          v-for="point in filteredPoints"
          :key="point[valueField]"
          :class="{ active: selectedPoint?.[valueField] === point[valueField] }"
          @tap="selectPoint(point)"
        >
          <view class="location-info">
            <text class="location-name">{{ point[labelField] }}</text>
          </view>
          <view class="location-check" v-if="selectedPoint?.[valueField] === point[valueField]">
            <uni-icons type="checkmarkempty" size="16" color="#007aff"></uni-icons>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="filteredPoints.length === 0" class="empty-state">
          <EmptyData v-if="searchKeyword" text="未找到相关点位" />
          <EmptyData v-else-if="selectedStructureId" text="该结构下暂无点位数据" />
          <EmptyData v-else text="请先选择结构" />
        </view>
      </scroll-view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import EmptyData from '@/components/EmptyData/EmptyData.vue'
import { pointList, pointCategory } from '@/api/device'

// 获取当前实例上下文
const { proxy } = getCurrentInstance()

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 当前选中的结构ID
  structureId: {
    type: [String, Number],
    default: '',
  },
  // 当前选中的点位ID
  selectedValue: {
    type: [String, Number],
    default: '',
  },
  // 结构显示文本字段名
  structureLabelField: {
    type: String,
    default: 'structureName',
  },
  // 结构选中值字段名
  structureValueField: {
    type: String,
    default: 'structureId',
  },
  // 点位显示文本字段名
  labelField: {
    type: String,
    default: 'pointName',
  },
  // 点位选中值字段名
  valueField: {
    type: String,
    default: 'id',
  },
})

// 定义事件
const emit = defineEmits([
  'update:modelValue',
  'update:selectedValue',
  'update:structureId',
  'confirm',
  'reset',
  'point-matched',
  'structure-selected',
])

// 搜索关键词
const searchKeyword = ref('')
// 选中的点位
const selectedPoint = ref(null)
// 所有点位数据
const allPoints = ref([])
// 所有结构数据
const structures = ref([])
// 当前选中的结构ID
const selectedStructureId = ref('')

// 过滤后的点位列表
const filteredPoints = computed(() => {
  let points = allPoints.value

  // 按搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    points = points.filter(item => {
      const labelValue = item[props.labelField]
      return labelValue && labelValue.toString().toLowerCase().includes(keyword)
    })
  }

  return points
})

// 监听选中值变化
watch(
  () => props.selectedValue,
  newValue => {
    if (newValue) {
      const point = allPoints.value.find(item => item[props.valueField] == newValue)
      selectedPoint.value = point || null

      // 如果找到匹配的点位，自动通知父组件更新显示信息
      if (point) {
        const result = {
          pointId: point[props.valueField],
          pointName: point[props.labelField]
        }
        // 通过自定义事件通知父组件点位信息已匹配
        emit('point-matched', result)
      }
    } else {
      selectedPoint.value = null
    }
  },
  { immediate: true }
)

// 监听structureId变化，重新加载点位数据
watch(
  () => props.structureId,
  newValue => {
    if (newValue) {
      selectedStructureId.value = newValue
      getPointData()
    }
  },
  { immediate: true }
)

// 获取结构数据
function getStructureData() {
  try {
    proxy.$modal.loading('加载结构数据...')
    pointCategory()
      .then(res => {
        structures.value = res.data || []

        // 如果有传入的结构ID，自动选中
        if (props.structureId && structures.value.length > 0) {
          selectedStructureId.value = props.structureId
          // 查找匹配的结构信息，通知父组件
          const structure = structures.value.find(item => item[props.structureValueField] == props.structureId)
          if (structure) {
            const result = {
              structureId: structure[props.structureValueField],
              structureName: structure[props.structureLabelField]
            }
            emit('structure-selected', result)
          }
          getPointData()
        }
      })
      .catch(error => {
        structures.value = []
        proxy.$modal.msgError('获取结构数据失败')
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  } catch (error) {
    structures.value = []
    proxy.$modal.msgError('获取结构数据失败')
    proxy.$modal.closeLoading()
  }
}

// 获取点位数据
function getPointData() {
  if (!selectedStructureId.value) {
    allPoints.value = []
    return
  }

  try {
    proxy.$modal.loading('加载点位数据...')
    pointList({ structureId: selectedStructureId.value })
      .then(res => {
        // 保持原始数据结构，支持动态字段映射
        allPoints.value = res.data || []

        // 数据加载完成后，如果有选中值，重新触发匹配逻辑
        if (props.selectedValue && allPoints.value.length > 0) {
          const point = allPoints.value.find(item => item[props.valueField] == props.selectedValue)
          if (point) {
            selectedPoint.value = point
            const result = {
              pointId: point[props.valueField],
              pointName: point[props.labelField]
            }
            // 通知父组件点位信息已匹配
            emit('point-matched', result)
          }
        }
      })
      .catch(error => {
        allPoints.value = []
        proxy.$modal.msgError('获取点位数据失败')
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  } catch (error) {
    allPoints.value = []
    proxy.$modal.msgError('获取点位数据失败')
    proxy.$modal.closeLoading()
  }
}

// 选择结构
function selectStructure(structure) {
  selectedStructureId.value = structure[props.structureValueField]
  // 清空已选点位
  selectedPoint.value = null
  emit('update:selectedValue', '')
  // 更新结构ID
  emit('update:structureId', structure[props.structureValueField])

  // 构造返回对象，只包含需要的字段
  const result = {
    structureId: structure[props.structureValueField],
    structureName: structure[props.structureLabelField]
  }
  emit('structure-selected', result)

  // 加载该结构下的点位
  getPointData()
}

// 选择点位
function selectPoint(point) {
  selectedPoint.value = point
  // 发出双向绑定事件
  emit('update:selectedValue', point[props.valueField])
}

// 清除搜索
function clearSearch() {
  searchKeyword.value = ''
}

// 确认选择
function handleConfirm() {
  if (!selectedStructureId.value) {
    proxy.$modal.msgError('请先选择结构')
    return
  }

  if (!selectedPoint.value) {
    proxy.$modal.msgError('请选择点位')
    return
  }

  // 查找选中的结构信息
  const structure = structures.value.find(item => item[props.structureValueField] == selectedStructureId.value)

  // 构造返回对象，只包含需要的结构和点位信息
  const result = {
    structureId: selectedStructureId.value,
    structureName: structure ? structure[props.structureLabelField] : '',
    pointId: selectedPoint.value[props.valueField],
    pointName: selectedPoint.value[props.labelField]
  }
  emit('confirm', result)
  emit('update:modelValue', false)
}

// 重置选择
function handleReset() {
  selectedPoint.value = null
  searchKeyword.value = ''
  // 重置时也要更新双向绑定的值
  emit('update:selectedValue', '')
  emit('reset')
}

onMounted(() => {
  // 获取结构数据
  getStructureData()
})
</script>

<style lang="scss" scoped>
.structure-container {
  margin-bottom: 30rpx;
}

.structure-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.structure-list {
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.structure-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    background-color: #f0f8ff;
  }
}

.structure-info {
  flex: 1;
}

.structure-name {
  font-size: 30rpx;
  color: #333;
  display: block;
}

.structure-check {
  margin-left: 20rpx;
}

.search-container {
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  color: #333;
  margin-left: 16rpx;
}

.search-clear {
  padding: 10rpx;
}

.location-container {
  flex: 1;
}

.location-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.location-list {
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
}

.location-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    background-color: #f0f8ff;
  }
}

.location-info {
  flex: 1;
}

.location-name {
  font-size: 30rpx;
  color: #333;
  display: block;
}

.location-check {
  margin-left: 20rpx;
}

.empty-state {
  padding: 40rpx 20rpx;
  text-align: center;
}
</style>
