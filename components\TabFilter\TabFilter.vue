<template>
  <view class="tab-filter">
    <!-- 选项卡区域 -->
    <view class="tab-container">
      <view class="tab-wrapper" :class="[`tab-wrapper--${styleType}`]">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="[`tab-item--${styleType}`, { 'tab-item--active': isTabActive(tab) }]"
          @tap="handleTabClick(tab, index)"
        >
          <text class="tab-text" :class="{ 'tab-text--active': isTabActive(tab) }">
            {{ getTabName(tab) }}
          </text>
          <!-- 指示条 - 仅在text模式下显示 -->
          <view
            v-if="styleType === 'text'"
            class="tab-indicator"
            :class="{ 'tab-indicator--active': isTabActive(tab) }"
          ></view>
        </view>
      </view>
    </view>

    <!-- 筛选按钮 -->
    <view class="filter-btn" @tap="handleFilterClick" v-if="showFilter">
      <image :src="filterIcon" mode="widthFix" style="width: 36rpx; height: 36rpx"></image>
      <text>{{ filterText }}</text>
    </view>

    <!-- 内容区域 -->
    <slot></slot>
  </view>
</template>

<script setup>
// 不需要导入任何Vue函数，因为我们只使用基本的响应式功能

// 定义props
const props = defineProps({
  // 选项卡数据
  tabs: {
    type: Array,
    default: () => [],
  },
  // 默认激活的选项卡值
  modelValue: {
    type: [String, Number, Boolean, Object],
    default: '',
  },
  // 选项卡名称的键名
  nameKey: {
    type: String,
    default: 'name',
  },
  // 选项卡值的键名
  valueKey: {
    type: String,
    default: 'value',
  },
  // 是否显示筛选按钮
  showFilter: {
    type: Boolean,
    default: true,
  },
  // 激活颜色
  activeColor: {
    type: String,
    default: '#2979ff',
  },
  // 非激活颜色
  inActiveColor: {
    type: String,
    default: '#f5f5f5',
  },
  // 样式类型：button 或 text
  styleType: {
    type: String,
    default: 'text',
  },
  // 筛选按钮文字
  filterText: {
    type: String,
    default: '筛选',
  },
  // 筛选按钮图标
  filterIcon: {
    type: String,
    default: '/static/images/filter.png',
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'tab-change', 'filter-click'])

/**
 * 获取tab名称
 * @param {String|Object} tab - tab数据
 * @returns {String} tab名称
 */
const getTabName = tab => {
  if (typeof tab === 'string') {
    return tab
  } else if (typeof tab === 'object' && tab[props.nameKey]) {
    return tab[props.nameKey]
  } else if (typeof tab === 'object' && tab.name) {
    return tab.name
  } else if (typeof tab === 'object' && tab.label) {
    return tab.label
  } else if (typeof tab === 'object' && tab.title) {
    return tab.title
  }
  return String(tab)
}

/**
 * 获取tab的值
 * @param {String|Object} tab - tab数据
 * @param {Number} index - tab索引，当无法获取值时使用索引作为备选
 * @returns {Any} tab值
 */
const getTabValue = (tab, index) => {
  if (typeof tab === 'string') {
    return tab
  } else if (typeof tab === 'object' && props.valueKey && tab[props.valueKey] !== undefined) {
    return tab[props.valueKey]
  } else if (typeof tab === 'object' && tab.value !== undefined) {
    return tab.value
  }
  // 如果没有指定valueKey或者找不到值，则返回索引
  return index
}

/**
 * 判断tab是否激活
 * @param {String|Object} tab - tab数据
 * @returns {Boolean} 是否激活
 */
const isTabActive = (tab) => {
  // 找到当前tab的索引
  const index = props.tabs.findIndex(item => item === tab)
  
  // 获取tab的值
  const value = getTabValue(tab, index)
  
  // 比较值是否相等
  return props.modelValue === value
}

/**
 * 处理tab点击事件
 * @param {Object|String} tab - 点击的tab数据
 * @param {Number} index - 点击的tab索引
 */
const handleTabClick = (tab, index) => {
  // 获取tab的值
  const value = getTabValue(tab, index)
  
  // 如果值没有变化，不执行后续操作
  if (value === props.modelValue) {
    return
  }

  // 触发双向绑定更新
  emit('update:modelValue', value)

  // 触发tab-change事件，保持与原有接口一致，但传递实际值而不是索引
  emit('tab-change', value, tab, index)
}

// 处理筛选按钮点击
const handleFilterClick = () => {
  emit('filter-click')
}
</script>

<style lang="scss" scoped>
.tab-filter {
  display: flex;
  position: relative;
  background-color: #fff;
  padding: 0 30rpx;
  align-items: center;
  justify-content: space-between;
}

.tab-container {
  flex: 1;
  display: flex;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }
}

/* Tab包装器 */
.tab-wrapper {
  display: flex;
  position: relative;

  /* button模式样式 */
  &.tab-wrapper--button {
    background-color: #f5f5f5;
    border-radius: 12rpx;
    padding: 6rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  /* text模式样式 */
  &.tab-wrapper--text {
    background-color: transparent;
    border-bottom: 2rpx solid #f0f0f0;
  }
}

/* Tab项 - 内容自适应宽度，不均分 */
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  padding: 0 24rpx; /* 添加左右内边距 */
  margin-right: 20rpx; /* tab项之间的间距 */

  &:last-child {
    margin-right: 0; /* 最后一个tab项不需要右边距 */
  }

  /* button模式样式 */
  &.tab-item--button {
    height: 80rpx;
    border-radius: 8rpx;
    background-color: transparent;

    &.tab-item--active {
      background-color: #fff;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  /* text模式样式 */
  &.tab-item--text {
    height: 88rpx;
    background-color: transparent;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 4rpx;
      background-color: #2979ff;
      transition: width 0.3s ease;
      border-radius: 2rpx;
    }

    &.tab-item--active {
      &::after {
        width: 60rpx;
      }
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

/* Tab文字 */
.tab-text {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  transition: color 0.3s ease;
  color: #666;
  white-space: nowrap; /* 防止文字换行 */

  /* button模式下的文字颜色 */
  .tab-item--button & {
    &.tab-text--active {
      color: #2979ff;
      font-weight: 600;
    }
  }

  /* text模式下的文字颜色 */
  .tab-item--text & {
    &.tab-text--active {
      color: #2979ff;
      font-weight: 600;
    }
  }
}

/* 指示条 - 仅text模式 */
.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 4rpx;
  background-color: #2979ff;
  border-radius: 2rpx;
  transition: all 0.3s ease;
  opacity: 0;
  width: 0;

  &.tab-indicator--active {
    opacity: 1;
    width: 60rpx; /* 固定宽度，只在文字下方 */
  }
}

.filter-btn {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  padding: 12rpx 24rpx;
  flex-shrink: 0;

  text {
    font-size: 36rpx;
    color: #222631;
    margin-left: 12rpx;
  }
}

/* 移动端优化 */
@media screen and (max-width: 750px) {
  .tab-text {
    font-size: 28rpx;
  }

  .tab-item--button {
    height: 72rpx;
  }

  .tab-item--text {
    height: 80rpx;
  }
}

/* 触摸反馈优化 */
.tab-item {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}
</style>
