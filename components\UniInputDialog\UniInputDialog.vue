<template>
  <!-- 基于 uni-popup-dialog 的全局输入弹窗 -->
  <uni-popup ref="popup" type="dialog" :mask-click="false">
    <uni-popup-dialog
      ref="inputDialog"
      mode="input"
      :title="currentOptions.title"
      :value="inputValue"
      :placeholder="currentOptions.placeholder"
      :maxlength="currentOptions.maxlength"
      :input-type="currentOptions.inputType"
      :focus="currentOptions.focus"
      :cancel-text="currentOptions.cancelText"
      :confirm-text="currentOptions.confirmText"
      @confirm="handleConfirm"
      @close="handleCancel"
    />
  </uni-popup>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 弹窗引用
const popup = ref(null)
const inputDialog = ref(null)
const inputValue = ref('')

// 当前弹窗配置
const currentOptions = ref({
  title: '请输入',
  placeholder: '请输入内容',
  value: '',
  maxlength: 200,
  inputType: 'text',
  focus: true,
  cancelText: '取消',
  confirmText: '确定',
  onConfirm: null,
  onCancel: null
})

// 打开弹窗
const open = (options = {}) => {
  // 合并配置
  currentOptions.value = {
    ...currentOptions.value,
    ...options
  }
  
  // 设置初始值
  inputValue.value = currentOptions.value.value || ''
  
  // 打开弹窗
  popup.value.open()
}

// 关闭弹窗
const close = () => {
  popup.value.close()
}

// 确认操作
const handleConfirm = (value) => {
  if (typeof currentOptions.value.onConfirm === 'function') {
    currentOptions.value.onConfirm(value)
  }
  close()
}

// 取消操作
const handleCancel = () => {
  if (typeof currentOptions.value.onCancel === 'function') {
    currentOptions.value.onCancel()
  }
  close()
}

// 监听全局事件
const handleUniInputDialog = (options) => {
  open(options)
}

// 生命周期
onMounted(() => {
  uni.$on('showUniInputDialog', handleUniInputDialog)
})

onUnmounted(() => {
  uni.$off('showUniInputDialog', handleUniInputDialog)
})

// 暴露方法
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
/* 可以在这里添加自定义样式覆盖默认样式 */
</style>
