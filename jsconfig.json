{"compilerOptions": {"target": "es2015", "module": "esnext", "baseUrl": ".", "moduleResolution": "node", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/api/*": ["./api/*"], "@/utils/*": ["./utils/*"], "@/static/*": ["./static/*"], "@/store/*": ["./store/*"], "@/pages/*": ["./pages/*"], "@/plugins/*": ["./plugins/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "types": ["uni-app"], "jsx": "preserve"}, "exclude": ["node_modules", "unpackage", "dist"], "include": ["**/*.js", "**/*.jsx", "**/*.vue", "**/*.json"]}