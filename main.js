import { createSSRApp } from 'vue'
import App from './App'
import store from './store' // store
import { install } from './plugins' // plugins
import './permission' // permission
import { useDict, useTypeList } from '@/utils/dict'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.config.globalProperties.useDict = useDict
  app.config.globalProperties.useTypeList = useTypeList
  install(app)
  return {
    app,
  }
}
