<template>
  <view class="alarm-container">
    <!-- 状态标签栏和筛选 -->
    <view class="status-tabs">
      <view class="tabs-left">
        <view class="tab-item" :class="{ active: activeTab === 'unfinished' }" @tap="switchTab('unfinished')">
          <text>未完成</text>
        </view>
        <view class="tab-item" :class="{ active: activeTab === 'finished' }" @tap="switchTab('finished')">
          <text>已完成</text>
        </view>
      </view>
      <view class="tabs-right" @tap="showFilter">
        <image src="/static/images/alarm/filter-icon.svg" class="filter-icon" mode="aspectFit"></image>
        <text class="filter-text">筛选</text>
      </view>
    </view>
    <!-- 报警列表 -->
    <view class="alarm-list-container">
      <scroll-view class="alarm-list" scroll-y="true">
        <CustomSwipeCard v-for="item in alarmList" :key="item.id" style="margin-bottom: 20rpx;">
          <template #content>
            1231
          </template>
        </CustomSwipeCard>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getAlarmList as getAlarmListApi } from '@/api/alarm'
import CustomSwipeCard from '@/components/CustomSwipeCard/CustomSwipeCard.vue'

const iconList = ref(
  {
    "1": '/static/images/alarm/icon1.png',
    "2": '/static/images/alarm/icon2.png',
    "3": '/static/images/alarm/icon3.png',
    "4": '/static/images/alarm/icon4.png',
  }
)

const queryParams = ref({
  StartTimeEnd: '',
  StartTimeStart: '',
  alarmCode: '',
  alarmState: 1,
  alarmType: '',
  endTimeEnd: '',
  endTimeStart: '',
  startTimeEnd: '',
  startTimeStart: '',
})
const activeTab = ref('unfinished')

const alarmList = ref([
  {
    alarmCode: '222',
    alarmEndTime: null,
    alarmState: '0',
    alarmStateName: '已完成',
    alarmSustainTime: null,
    alarmType: '0',
    alarmTypeName: '态势预警',
    confirmMark: true,
    confirmTime: '2025-02-24 16:25:44',
    confirmTimeLeft: null,
    confirmTimeOut: '1164h41m40s',
    handleConfirmMark: true,
    handleConfirmTime: null,
    handleConfirmTimeLeft: null,
    handleConfirmTimeOut: '37h27m11s',
    id: '249',
    investigateConfirmMark: null,
    investigateTime: null,
    investigateTimeLeft: null,
    investigateTimeOut: null,
    pointName: '点位1',
    reliability: '5',
    startTime: '2025-02-24 16:25:38',
    updateTime: '2025-04-14T15:51:24.000+08:00',
  },
])

const switchTab = tab => {
  activeTab.value = tab
}

const showFilter = () => {
  // 筛选功能
}

const handleAction = item => {
  // 处理操作
}

const getAlarmList = () => {
  // 获取报警列表
  getAlarmListApi(queryParams.value).then(resp => {
    alarmList.value = resp.data
    console.log(alarmList.value)
  })
}

onMounted(() => {
  getAlarmList()
})
</script>

<style lang="scss" scoped>
.alarm-container {
  width: 100%;
  min-height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 状态标签栏和筛选 */
.status-tabs {
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  border-bottom: 2rpx solid #e5e5e5;
  flex-shrink: 0;

  .tabs-left {
    display: flex;
    align-items: center;

    .tab-item {
      margin-right: 60rpx;
      padding-bottom: 20rpx;
      position: relative;

      text {
        font-size: 32rpx;
        color: #999;
      }

      &.active {
        text {
          color: #007aff;
          font-weight: 500;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 4rpx;
          background-color: #007aff;
          border-radius: 2rpx;
        }
      }
    }
  }

  .tabs-right {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx 24rpx;
    cursor: pointer;
    gap: 8rpx;

    .filter-icon {
      width: 32rpx;
      height: 32rpx;
    }

    .filter-text {
      font-size: 28rpx;
      color: #666;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 8rpx;
    }
  }
}
.alarm-list-container {
  flex: 1;
  padding: 20rpx;
  height: 0;
}

</style>
