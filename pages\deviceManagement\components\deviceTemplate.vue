<template>
  <view class="device-card" @tap="handleClick">
    <!-- 设备标题和状态 -->
    <view class="device-header">
      <view class="device-title">
        <image :src="deviceIcon" class="device-icon" mode="aspectFit"></image>
        <text class="device-id">{{  `${deviceData.structureName}-${deviceData.pointName}` }}</text>
        <view class="device-tag" v-if="deviceData.categoryName">{{ deviceData.categoryName}}</view>
      </view>
    </view>

    <!-- 设备详情 -->
    <view class="device-info">
      <view class="info-row">
        <view class="info-item">
          <text class="info-label">设备规格</text>
          <text class="info-value">{{ deviceData.specifications}}</text>
          <text class="info-value" v-if="!deviceData.specifications">暂无设备规格</text>
        </view>
        <view class="info-item">
          <text class="info-label">设备型号</text>
          <text class="info-value">{{ deviceData.model}}</text>
          <text class="info-value" v-if="!deviceData.model">暂无设备型号</text>
        </view>
      </view>
    </view>
    <!-- 加一条分割线 -->
    <view class="device-line"></view>
    <view class="device-info-container">
      <!-- 设备位置和状态信息一行显示 -->
      <view class="location-status-row">
        <!-- 设备位置 -->
        <view class="device-location">
          <uni-icons type="location" size="30" color="#999"></uni-icons>
          <text class="location-text">{{ deviceData.pointName }}</text>
          <text class="location-text" v-if="!deviceData.pointName">暂无设备位置</text>
        </view>

        <!-- 设备状态横向排列 -->
        <view class="device-status-row">
          <view class="status-item">
            <text class="status-label">设备状态</text>
            <view class="status-value">
              <view class="status-dot" :class="deviceStatusDotClass"></view>
              <text>{{ deviceStateText }}</text>
            </view>
          </view>
          <view class="status-sep"></view>
          <view class="status-item">
            <text class="status-label">隔离状态</text>
            <view class="status-value">
              <view class="status-dot" :class="isolateStatusDotClass"></view>
              <text>{{ isolateStateText }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  deviceData: {
    type: Object,
    default: () => ({}),
  }
})

// 定义事件
const emit = defineEmits(['click'])

// 处理点击事件
const handleClick = () => {
  emit('click', props.deviceData)
}

// 根据设备类型计算设备图标
const deviceIcon = computed(() => {
  return props.deviceData.deviceImg || '/static/images/device/probe.png'
})

// 计算设备状态文本
const deviceStateText = computed(() => {
  const stateMap = {
    0: '离线',
    1: '在线',
    3: '故障',
    // 可以根据实际状态码添加更多映射
  }
  return stateMap[props.deviceData.deviceState] || '--'
})

// 计算设备状态点样式
const deviceStatusDotClass = computed(() => {
  const classMap = {
    0: 'offline',
    1: 'normal',
    3: 'error',
    // 可以根据实际状态码添加更多映射
  }
  return classMap[props.deviceData.deviceState] || '--'
})

// 计算隔离状态文本
const isolateStateText = computed(() => {
  const stateMap = {
    0: '隔离中',
    1: '未隔离',
    // 可以根据实际状态码添加更多映射
  }
  return stateMap[props.deviceData.isolateState] || '--'
})

// 计算隔离状态点样式
const isolateStatusDotClass = computed(() => {
  const classMap = {
    0: 'warning',
    1: 'normal',
    // 可以根据实际状态码添加更多映射
  }
  return classMap[props.deviceData.isolateState] || '--'
})
</script>

<style lang="scss" scoped>
.device-card {
  padding: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.device-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.device-title {
  display: flex;
  align-items: center;
}

.device-icon {
  width: 48rpx;
  height: 48rpx;
}

.device-id {
  font-size: 38rpx;
  color: #222631;
  margin-left: 16rpx;
}

.device-tag {
  background: rgba(255, 255, 255, 0.1);
  color: #3370ff;
  border-radius: 8rpx;
  padding: 4rpx 16rpx;
  margin-left: 16rpx;
  font-size: 28rpx;
  border: 1px solid #3370ff;
}

.device-info {
  margin-bottom: 32rpx;
}

.info-row {
  display: flex;
  justify-content: flex-start;
  padding-left: 64rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 170rpx;
}

.info-label {
  font-size: 38rpx;
  color: #666;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 38rpx;
  color: #666;
  flex-shrink: 0;
}

.device-line {
  width: 100%;
  height: 1rpx;
  background: #132b3f;
  opacity: 0.1;
}

.device-info-container {
  padding-top: 32rpx;
}

.location-status-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.device-location {
  display: flex;
  align-items: center;
}

.location-text {
  font-size: 32rpx;
  color: #666;
  margin-left: 8rpx;
}

.device-status-row {
  display: flex;
  align-items: center;
}

.status-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.status-label {
  font-size: 28rpx;
  color: #999;
  margin-right: 16rpx;
}

.status-value {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-dot.normal {
  background-color: #27a376;
}

.status-dot.offline {
  background-color: #999;
}

.status-dot.error {
  background-color: #ff3b30;
}

.status-dot.warning {
  background-color: #ff9500;
}

.status-sep {
  width: 1rpx;
  height: 40rpx;
  background-color: #e5e5e5;
  margin: 0 40rpx;
}
</style>
