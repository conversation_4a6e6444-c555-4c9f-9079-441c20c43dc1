<template>
  <CustomPopup
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="选择具体位置"
    @confirm="handleConfirm"
    @reset="handleReset"
  >
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input
          v-model="searchKeyword"
          class="search-input"
          placeholder="搜索点位名称"
          placeholder-style="color: #999;"
        />
        <view v-if="searchKeyword" class="search-clear" @tap="clearSearch">
          <uni-icons type="clear" size="14" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 点位列表 -->
    <view class="location-container">
      <view class="location-title">
        {{ searchKeyword ? `搜索结果 (${filteredPoints.length})` : '选择点位' }}
      </view>
      <scroll-view class="location-list" scroll-y="true" style="height: 400rpx">
        <view
          class="location-item"
          v-for="point in filteredPoints"
          :key="point[valueField]"
          :class="{ active: selectedPoint?.[valueField] === point[valueField] }"
          @tap="selectPoint(point)"
        >
          <view class="location-info">
            <text class="location-name">{{ point[labelField] }}</text>
          </view>
          <view class="location-check" v-if="selectedPoint?.[valueField] === point[valueField]">
            <uni-icons type="checkmarkempty" size="16" color="#007aff"></uni-icons>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="filteredPoints.length === 0" class="empty-state">
          <text class="empty-text">{{ searchKeyword ? '未找到相关点位' : '暂无点位数据' }}</text>
        </view>
      </scroll-view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import { pointCategory } from '@/api/device'

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 当前选中的点位ID
  selectedValue: {
    type: [String, Number],
    default: '',
  },
  // 显示文本字段名
  labelField: {
    type: String,
    default: 'name',
  },
  // 选中值字段名
  valueField: {
    type: String,
    default: 'id',
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'update:selectedValue', 'confirm', 'reset', 'point-matched'])

// 搜索关键词
const searchKeyword = ref('')
// 选中的点位
const selectedPoint = ref(null)
// 所有点位数据
const allPoints = ref([])

// 过滤后的点位列表
const filteredPoints = computed(() => {
  let points = allPoints.value

  // 按搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    points = points.filter(item => {
      const labelValue = item[props.labelField]
      return labelValue && labelValue.toString().toLowerCase().includes(keyword)
    })
  }

  return points
})

// 监听选中值变化
watch(
  () => props.selectedValue,
  newValue => {
    if (newValue) {
      const point = allPoints.value.find(item => item[props.valueField] == newValue)
      selectedPoint.value = point || null

      // 如果找到匹配的点位，自动通知父组件更新显示信息
      if (point) {
        const result = {
          [props.valueField]: point[props.valueField],
          [props.labelField]: point[props.labelField],
          // 为了向后兼容，也保留原有的id和name字段
          id: point[props.valueField],
          name: point[props.labelField],
        }
        // 通过自定义事件通知父组件点位信息已匹配
        emit('point-matched', result)
      }
    } else {
      selectedPoint.value = null
    }
  },
  { immediate: true }
)

// 获取点位数据
const getPointData = async () => {
  try {
    const res = await pointCategory()
    // 保持原始数据结构，支持动态字段映射
    allPoints.value = res.data.map(item => ({
      // 保留原始字段
      structureId: item.structureId,
      structureName: item.structureName,
      // 为了向后兼容，也保留id和name字段
      id: item.structureId,
      name: item.structureName,
    }))
  } catch (error) {
    allPoints.value = []
    proxy.$modal.msgError('获取点位数据失败')
  }

  // 数据加载完成后，如果有选中值，重新触发匹配逻辑
  if (props.selectedValue && allPoints.value.length > 0) {
    const point = allPoints.value.find(item => item[props.valueField] == props.selectedValue)
    if (point) {
      selectedPoint.value = point
      const result = {
        [props.valueField]: point[props.valueField],
        [props.labelField]: point[props.labelField],
        // 为了向后兼容，也保留原有的id和name字段
        id: point[props.valueField],
        name: point[props.labelField],
      }
      // 通知父组件点位信息已匹配
      emit('point-matched', result)
    }
  }
}

// 选择点位
const selectPoint = point => {
  selectedPoint.value = point
  // 发出双向绑定事件
  emit('update:selectedValue', point[props.valueField])
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
}

// 确认选择
const handleConfirm = () => {
  if (selectedPoint.value) {
    // 构造返回对象，包含动态字段
    const result = {
      [props.valueField]: selectedPoint.value[props.valueField],
      [props.labelField]: selectedPoint.value[props.labelField],
      // 为了向后兼容，也保留原有的id和name字段
      id: selectedPoint.value[props.valueField],
      name: selectedPoint.value[props.labelField],
    }
    emit('confirm', result)
  }
  emit('update:modelValue', false)
}

// 重置选择
const handleReset = () => {
  selectedPoint.value = null
  searchKeyword.value = ''
  // 重置时也要更新双向绑定的值
  emit('update:selectedValue', '')
  emit('reset')
}

onMounted(() => {
  getPointData()
})
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  color: #333;
  margin-left: 16rpx;
}

.search-clear {
  padding: 10rpx;
}

.location-container {
  flex: 1;
}

.location-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.location-list {
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
}

.location-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    background-color: #f0f8ff;
  }
}

.location-info {
  flex: 1;
}

.location-name {
  font-size: 30rpx;
  color: #333;
  display: block;
}

.location-check {
  margin-left: 20rpx;
}

.empty-state {
  padding: 80rpx 20rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
