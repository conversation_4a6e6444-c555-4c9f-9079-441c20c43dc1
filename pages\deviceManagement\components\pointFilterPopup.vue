<template>
  <CustomPopup
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="筛选"
    @confirm="handleConfirm"
    @reset="handleReset"
  >
    <!-- 设备名称（在按点位模式下也可以筛选设备名称） -->
    <view class="filter-item">
      <text class="filter-label">设备名称</text>
      <input
        v-model="localFilterData.deviceName"
        class="filter-input"
        placeholder="请输入设备名称"
        placeholder-style="color: #999;"
      />
    </view>

    <!-- 设备型号 -->
    <view class="filter-item">
      <text class="filter-label">设备型号</text>
      <input
        v-model="localFilterData.model"
        class="filter-input"
        placeholder="请输入设备型号"
        placeholder-style="color: #999;"
      />
    </view>

    <!-- 设备规格 -->
    <view class="filter-item">
      <text class="filter-label">设备规格</text>
      <input
        v-model="localFilterData.specifications"
        class="filter-input"
        placeholder="请输入设备规格"
        placeholder-style="color: #999;"
      />
    </view>

    <!-- 设备类型 -->
    <view class="filter-item">
      <text class="filter-label">设备类型</text>
      <view class="filter-type-list">
        <view 
          class="filter-type-item" 
          v-for="item in device_category" 
          :key="item.value"
          :class="{ active: localFilterData.type.includes(item.value) }"
          @tap="toggleDeviceType(item.value)"
        >
          <text>{{ item.label }}</text>
        </view>
      </view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { watch, reactive, getCurrentInstance } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'

const { proxy } = getCurrentInstance()
const { device_category } = proxy.useTypeList(['device_category'])

// 定义props
const props = defineProps({
  // 控制弹窗显示
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 筛选数据
  filterData: {
    type: Object,
    default: () => ({
      deviceName: '',
      model: '',
      specifications: '',
      structureId: 0,
      type: [],
    }),
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm', 'reset'])

// 本地筛选数据（避免直接修改props）
const localFilterData = reactive({
  deviceName: '',
  model: '',
  specifications: '',
  structureId: 0,
  type: [],
})

// 监听筛选数据变化，同步到本地数据
watch(
  () => props.filterData,
  newData => {
    Object.assign(localFilterData, {
      deviceName: newData.deviceName,
      model: newData.model,
      specifications: newData.specifications,
      structureId: newData.structureId,
      type: [...(newData.type || [])],
    })
  },
  { immediate: true, deep: true }
)

// 选择点位分类（单选）
const selectPointCategory = value => {
  // 如果点击的是已选中的分类，则取消选择
  if (localFilterData.structureId === value) {
    localFilterData.structureId = 0
  } else {
    localFilterData.structureId = value
  }
}

// 切换设备类型（多选）
const toggleDeviceType = value => {
  const index = localFilterData.type.indexOf(value)
  if (index > -1) {
    // 如果已选中，则取消选择
    localFilterData.type.splice(index, 1)
  } else {
    // 如果未选中，则添加选择
    localFilterData.type.push(value)
  }
}

// 确认筛选
const handleConfirm = () => {
  emit('confirm', { ...localFilterData })
  emit('update:modelValue', false)
}

// 重置筛选
const handleReset = () => {
  localFilterData.deviceName = ''
  localFilterData.model = ''
  localFilterData.specifications = ''
  localFilterData.structureId = 0
  localFilterData.type = []
  emit('reset', { ...localFilterData })
}
</script>

<style lang="scss" scoped>
.filter-item {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 500;
}

.filter-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;

  &:focus {
    border-color: #007aff;
  }
}

.filter-type-list {
  display: flex;
  flex-wrap: wrap;
}

.filter-type-item {
  padding: 16rpx 24rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  
  &.active {
    background-color: #007aff;
    border-color: #007aff;
    color: #fff;
    
    text {
      color: #fff;
    }
  }
}
</style>
