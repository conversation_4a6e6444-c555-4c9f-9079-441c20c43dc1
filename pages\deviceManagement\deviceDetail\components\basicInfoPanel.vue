<template>
  <view class="basic-info-panel">
    <!-- 设备基本信息卡片 -->
    <view class="info-card">
      <!-- 设备详细信息列表 - 严格按照设计图单列布局 -->
      <view class="device-info-list">
        <!-- 设备名称 -->
        <view class="info-item">
          <text class="info-label">设备名称</text>
          <text class="info-value">{{ deviceData.deviceName }}</text>
        </view>

        <!-- 设备编号 -->
        <view class="info-item">
          <text class="info-label">设备编号</text>
          <text class="info-value">{{ deviceData.code }}</text>
        </view>

        <!-- 设备类型 -->
        <view class="info-item">
          <text class="info-label">设备类型</text>
          <text class="info-value">{{ deviceData.categoryName}}</text>
        </view>

        <!-- 设备规格 -->
        <view class="info-item">
          <text class="info-label">设备规格</text>
          <text class="info-value">{{ deviceData.specifications}}</text>
        </view>

        <!-- 设备型号 -->
        <view class="info-item">
          <text class="info-label">设备型号</text>
          <text class="info-value">{{ deviceData.model }}</text>
        </view>

        <!-- 设备位置 -->
        <view class="info-item">
          <text class="info-label">设备位置</text>
          <text class="info-value">{{ deviceData.pointName }}</text>
        </view>

        <!-- 设备状态 -->
        <view class="info-item">
          <text class="info-label">设备状态</text>
          <view class="status-value">
            <view class="status-dot" :class="deviceStatusDotClass"></view>
            <text>{{ deviceStateText }}</text>
          </view>
        </view>

        <!-- 隔离状态 -->
        <view class="info-item">
          <text class="info-label">隔离状态</text>
          <view class="status-value">
            <view class="status-dot" :class="isolateStatusDotClass"></view>
            <text>{{ isolateStateText }}</text>
          </view>
        </view>

        <!-- 图片 -->
        <view class="info-item">
          <text class="info-label">图片</text>
          <view v-if="deviceData.deviceImg">
            <text class="info-value link-text" @click="handleImageClick(deviceData.deviceImg)">查看 ></text>
          </view>
          <view v-else>
            <text class="info-value">暂无图片</text>
          </view>
        </view>

        <!-- 设备标识 -->
        <view class="info-item">
          <text class="info-label">设备标识</text>
          <text class="info-value">{{ deviceData.sign }}</text>
        </view>

        <!-- 证书编号 -->
        <view class="info-item">
          <text class="info-label">证书编号</text>
          <text class="info-value">{{ deviceData.certificateNo }}</text>
        </view>

        <!-- 证书图片 -->
        <view class="info-item">
          <text class="info-label">证书图片</text>
          <view v-if="deviceData.certificate">
            <text class="info-value link-text" @click="handleImageClick(deviceData.certificate)">查看 ></text>
          </view>
          <view v-else>
            <text class="info-value">暂无图片</text>
          </view>
        </view>

        <!-- 备注 -->
        <view class="info-item">
          <text class="info-label">备注</text>
          <text class="info-value">{{ deviceData.remark }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

/**
 * 设备基本信息面板组件
 * @description 显示设备的详细基本信息
 */

const props = defineProps({
  deviceData: {
    type: Object,
    default: () => ({}),
  },
})


// 计算设备状态文本
const deviceStateText = computed(() => {
  const stateMap = {
    0: '离线',
    1: '在线',
    3: '故障',
  }
  return stateMap[props.deviceData.deviceState] || '离线'
})

// 计算设备状态点样式
const deviceStatusDotClass = computed(() => {
  const classMap = {
    0: 'offline',
    1: 'normal',
    3: 'error',
  }
  return classMap[props.deviceData.deviceState] || 'offline'
})

// 计算隔离状态文本
const isolateStateText = computed(() => {
  const stateMap = {
    0: '隔离中',
    1: '未隔离',
  }
  return stateMap[props.deviceData.isolateState] || '隔离中'
})

// 计算隔离状态点样式
const isolateStatusDotClass = computed(() => {
  const classMap = {
    0: 'warning',
    1: 'normal',
  }
  return classMap[props.deviceData.isolateState] || 'warning'
})

const handleImageClick = (imgUrl) => {
  uni.previewImage({
    urls: [imgUrl],
  })
}
</script>

<style lang="scss" scoped>
.basic-info-panel {
  width: 100%;
  height: 100%;
}

.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.device-header {
  margin-bottom: 40rpx;
  padding-bottom: 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.device-title {
  display: flex;
  align-items: center;
}

.device-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.device-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #222631;
  margin-right: 16rpx;
}

.device-tag {
  background: rgba(255, 255, 255, 0.1);
  color: #3370ff;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 28rpx;
  border: 1px solid #3370ff;
}

.device-info-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 32rpx;
  color: #999;
  flex-shrink: 0;
  min-width: 160rpx;
}

.info-value {
  font-size: 32rpx;
  color: #333;
  text-align: right;

  &.link-text {
    color: #2979ff;
  }
}

.status-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-dot {
  position: relative;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  z-index: 1;
}

.status-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  z-index: -1;
}

.status-dot.normal {
  background-color: #00c853;
}

.status-dot.normal::before {
  background-color: rgba(0, 200, 83, 0.2);
}

.status-dot.error {
  background-color: #ff0000;
}

.status-dot.error::before {
  background-color: rgba(255, 0, 0, 0.2);
}

.status-dot.warning {
  background-color: #ffa500;
}

.status-dot.warning::before {
  background-color: rgba(255, 165, 0, 0.2);
}

.status-dot.offline {
  background-color: #9e9e9e;
}

.status-dot.offline::before {
  background-color: rgba(158, 158, 158, 0.2);
}

.status-value text {
  font-size: 32rpx;
  color: #333;
}
</style>
