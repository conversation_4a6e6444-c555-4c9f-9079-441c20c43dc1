<template>
  <view class="maintenance-records-panel">
    <!-- 表格形式的记录列表 -->
    <uni-table border stripe emptyText="暂无维保记录" :loading="loading">
      <!-- 表头行 -->
      <uni-tr class="table-header-row">
        <uni-th align="center">操作时间</uni-th>
        <uni-th align="center">操作类型</uni-th>
        <uni-th align="center" v-if="sortType === 0">设备名称</uni-th>
        <uni-th align="center" v-if="sortType === 0">设备编号</uni-th>
        <uni-th align="center" v-if="sortType === 1">具体点位</uni-th>
        <uni-th align="center">备注</uni-th>
      </uni-tr>
      <!-- 表格数据行 -->
      <uni-tr v-for="(record, index) in records" :key="record.id || index" class="table-data-row">
        <uni-td align="center">{{ formatDateTime(record.operDate) }}</uni-td>
        <uni-td align="center">
          <view class="operation-type" :class="getStatusClass(record.type)">
            {{ record.type }}
          </view>
        </uni-td>
        <uni-td align="center" v-if="sortType === 0">{{ record.deviceName }}</uni-td>
        <uni-td align="center" v-if="sortType === 0">{{ record.code }}</uni-td>
        <uni-td align="center" v-if="sortType === 1">{{ record.pointName }}</uni-td>
        <uni-td align="center">{{ record.remark || '-' }}</uni-td>
      </uni-tr>
    </uni-table>
  </view>
</template>

<script setup>
import { ref } from 'vue'

/**
 * 维保记录面板组件
 * @description 显示设备的维保记录列表
 */

const props = defineProps({
  records: {
    type: Array,
    default: () => [],
  },
  sortType: {
    type: Number,
    default: 0, // 默认按设备排序 0:按设备 1:按点位
  }
})

const loading = ref(false)

/**
 * 格式化完整时间显示
 * @param {String} timeStr - 时间字符串
 * @returns {String} 格式化后的完整时间
 */
const formatDateTime = timeStr => {
  if (!timeStr) return '--'

  try {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    return timeStr || '--'
  }
}

/**
 * 获取状态样式类名
 * @param {String} type - 操作类型
 * @returns {String} 样式类名
 */
const getStatusClass = type => {
  const classMap = {
    添加: 'add',
    移除: 'remove',
    更换: 'replace'
  }
  return classMap[type] || 'default'
}
</script>

<style lang="scss" scoped>
.maintenance-records-panel {
  width: 100%;
  height: 100%;
}

/* 自定义表格样式 */
:deep(.uni-table) {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

:deep(.table-header-row) {
  background-color: #f5f7fa !important;
}

:deep(.uni-th) {
  font-weight: 600;
  color: #333;
  padding: 24rpx 10rpx;
}

:deep(.uni-tbody) {
  .table-data-row {
    transition: background-color 0.3s;
    
    &:hover {
      background-color: rgba(41, 121, 255, 0.05);
    }
  }
}

:deep(.uni-td) {
  padding: 20rpx 10rpx;
}

.operation-type {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 28rpx;

  &.add {
    background-color: rgba(41, 121, 255, 0.1);
    color: #2979ff;
  }

  &.remove {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff0000;
  }

  &.replace {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
  }

  &.default {
    background-color: rgba(158, 158, 158, 0.1);
    color: #9e9e9e;
  }
}
</style>
