<template>
  <view class="device-management-container">
    <view class="device-management-header">
      <!-- 顶部tab -->
      <TabFilter @filter-click="handleFilterClick" />
      <!-- 详情类型 -->
      <view class="detail-type-container">
        <view
          class="detail-type-item"
          v-for="item in pointCategoryList"
          :key="item.value"
          :class="{ active: searchParams.structureId == item.value }"
          @tap="handleDetailTypeClick(item.value)"
        >
          <text>{{ item.label }}</text>
        </view>
      </view>
    </view>

    <!-- 设备列表 -->
    <view class="device-list-container">
      <CustomSwipeCard
        class="device-list-item"
        v-for="item in deviceListData"
        :key="item.structureId"
        :custom-swipe-options="customSwipeOptions"
        :current-data="item"
        @swipe-click="handleSwipeClick"
      >
        <template #content>
          <DeviceTemplate :device-data="item" @click="handleDeviceClick(item)" />
        </template>
      </CustomSwipeCard>
      <view class="no-data-container" v-if="deviceListData.length === 0">
        <view class="no-data-icon">
          <uni-icons type="info-filled" size="64" color="#CCCCCC"></uni-icons>
        </view>
        <text class="no-data-text">暂无设备数据</text>
        <text class="no-data-tips">请尝试更换筛选条件</text>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <!-- 按点位筛选弹窗 -->
    <PointFilterPopup
      v-model="showPointFilter"
      :filter-data="tempFilterParams"
      @confirm="handlePointFilterConfirm"
      @reset="handlePointFilterReset"
    />
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { pointCategory, deviceList } from '@/api/device'
import TabFilter from '@/components/TabFilter/TabFilter.vue'
import CustomSwipeCard from '@/components/CustomSwipeCard/CustomSwipeCard.vue'
import DeviceTemplate from './components/deviceTemplate.vue'
import PointFilterPopup from './components/pointFilterPopup.vue'
import { reactive } from 'vue'
import { buildNavigateUrl } from '@/utils/url'

const { proxy } = getCurrentInstance()

const customSwipeOptions = ref([
  {
    text: '维保',
    style: {
      backgroundColor: '#27a376',
    },
  },
])

const searchParams = reactive({
  deviceName: '', // 设备名称
  structureId: 0, // 点位id
  model: '', // 设备型号
  specifications: '', // 设备规格
  type: [], // 筛选弹窗里面的设备类型 id 可以多选
})

// 筛选弹窗显示状态
const showPointFilter = ref(false)

// 临时筛选条件（用户操作过程中的数据）
const tempFilterParams = reactive({
  deviceName: '',
  model: '',
  specifications: '',
  structureId: 0,
  type: [],
})

function handleDetailTypeClick(value) {
  // 如果点击的是已选中的点位，则不做任何操作
  if (searchParams.structureId === value) {
    return
  }

  searchParams.structureId = value
  getDeviceList()
}

// 获取点位分类
const pointCategoryList = ref([])
async function getPointCategoryList() {
  try {
    const res = await pointCategory()
    pointCategoryList.value = res.data.map(item => ({
      value: item.structureId,
      label: item.structureName,
    }))

    // 如果有点位数据，默认选中第一个并请求设备列表
    if (pointCategoryList.value.length > 0) {
      searchParams.structureId = pointCategoryList.value[0].value
      getDeviceList()
    }
  } catch (error) {
    console.error('获取点位分类失败:', error)
  }
}

// 初始化临时筛选条件
function initTempFilterParams() {
  Object.assign(tempFilterParams, {
    deviceName: searchParams.deviceName,
    model: searchParams.model,
    specifications: searchParams.specifications,
    structureId: searchParams.structureId,
    type: searchParams.type,
  })
}

// 处理筛选按钮点击
function handleFilterClick() {
  // 初始化临时筛选条件
  initTempFilterParams()
  // 显示点位筛选弹窗
  showPointFilter.value = true
}

const deviceListData = ref([])
// 获取设备列表
async function getDeviceList() {
  const res = await deviceList(searchParams)
  deviceListData.value = res.data
}

function handleSwipeClick(e) {
  const { currentData } = e

  const params = {
    deviceId: currentData.id || '',
    pointId: currentData.structureId || '',
    categoryId: currentData.categoryId || '',
    categoryName: currentData.categoryName || '',
  }

  // 使用工具函数构建 URL
  const url = buildNavigateUrl('/pages/deviceManagement/maintenanceRecords', params)
  proxy.$tab.navigateTo(url)
}

// 处理设备卡片点击事件
function handleDeviceClick(deviceData) {
  const { id } = deviceData
  if (!id) {
    return proxy.$modal.showToast('暂无设备详情')
  } else {
    // 跳转到设备详情页面
    proxy.$tab.navigateTo(`/pages/deviceManagement/deviceDetail/index?deviceId=${id}`)
  }
}

// 按点位筛选确认
function handlePointFilterConfirm(filterData) {
  // 应用筛选条件到searchParams
  Object.assign(searchParams, {
    deviceName: filterData.deviceName,
    model: filterData.model,
    specifications: filterData.specifications,
    type: filterData.type,
  })
  // 刷新设备列表
  getDeviceList()
}

// 按点位筛选重置
function handlePointFilterReset(filterData) {
  // 重置临时筛选条件
  Object.assign(tempFilterParams, filterData)
}

onMounted(async () => {
  // 先获取点位分类数据
  await getPointCategoryList()
  // 再获取设备列表
  getDeviceList()
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.device-management-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: $uni-height-area;
}

.detail-type-container {
  display: flex;
  width: 100%;
  padding: 24rpx 30rpx;
  gap: 18rpx;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.detail-type-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  padding: 20rpx 30rpx;
  flex-shrink: 0;
  margin-right: 18rpx;
  text {
    font-size: 36rpx;
    color: #666;
  }
  &.active {
    background-color: #007aff;
    text {
      color: #fff;
    }
  }
}

.device-list-container {
  flex: 1;
  flex-shrink: 0;
  padding: 0 32rpx 32rpx 32rpx;
  min-height: 0;
  overflow-y: auto;
}

.device-list-item {
  margin-bottom: 32rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 100rpx 0;
}

.no-data-icon {
  margin-bottom: 30rpx;
}

.no-data-text {
  font-size: 36rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.no-data-tips {
  font-size: 28rpx;
  color: #999;
}
</style>
