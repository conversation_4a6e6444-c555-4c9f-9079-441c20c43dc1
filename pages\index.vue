<template>
  <view class="map-container" :style="{ backgroundImage: 'url(' + bgImage + ')' }">
    <!-- 顶部信息栏 -->
    <view class="top-bar">
      <view class="info-box">
        <view class="navigation-info">
          <view class="info-row">
            <text class="info-label">航向</text>
            <text class="info-value">157.8°</text>
            <text class="info-label">船向</text>
            <text class="info-value">157.8°</text>
            <text class="info-label">航速</text>
            <text class="info-value">10.1节</text>
          </view>
          <view class="coordinates">
            <text>122°37.9776'E, 29°54.4490'N</text>
          </view>
        </view>
      </view>
      <view class="info-box">
        <view class="time-info">
          <view class="time-row">
            <text class="time-label">北京时间</text>
            <text class="time-value">2025-03-04 21:10:24</text>
          </view>
          <view class="time-row">
            <text class="time-label">UTC时间</text>
            <text class="time-value">2025-03-04 21:10:24</text>
          </view>
        </view>
      </view>

      <view class="spacer"></view>

      <view class="info-box weather-box">
        <view class="weather-content">
          <view class="weather-main">
            <image src="/static/images/weather/sunny.png" mode="aspectFit" class="weather-icon"></image>
            <text class="temperature">25°C</text>
          </view>
          <view class="weather-details">
            <text class="humidity"> <i class="iconfont icon-humidity"></i> 26% </text>
            <text class="wind"> <i class="iconfont icon-wind"></i> 11 西北风 </text>
          </view>
        </view>
      </view>

      <view class="info-box connection-status">
        <view class="status-item">
          <view class="status-dot green"></view>
          <text>服务器已连接</text>
        </view>
        <view class="status-item">
          <view class="status-dot green"></view>
          <text>硬件已连接</text>
        </view>
      </view>
    </view>

    <!-- 右侧功能菜单 -->
    <view class="right-menu">
      <view class="menu-item" v-for="item in rightMenuList" :key="item.name" @tap="jumpToPage(item.jump)">
        <i :class="item.icon"></i>
        <view class="menu-text-row">
          <text>{{ item.name }}</text>
          <view class="badge" v-if="item.badge">{{ item.badge }}</view>
        </view>
      </view>
      <view class="menu-separator"></view>
      <view class="menu-toggle">
        <i class="iconfont icon-arrow-left"></i>
      </view>
    </view>

    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item active">
        <text>态势预警</text>
      </view>
      <view class="nav-item">
        <text>报警信息</text>
      </view>
      <view class="nav-item">
        <text>设备监测</text>
      </view>
    </view>

    <!-- 设置按钮 -->
    <view class="settings-button">
      <i class="iconfont icon-settings"></i>
    </view>
  </view>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive } from 'vue'

const bgImage = ref('/static/images/banner/banner01.jpg')

// 右侧功能菜单列表
const rightMenuList = reactive([
  {
    icon: 'icon-monitor',
    name: '监控',
    badge: 15,
    jump: '',
  },
  {
    icon: 'icon-alarm',
    name: '警报',
    badge: 15,
    jump: '/pages/alarm/index',
  },
  {
    icon: 'icon-patrol',
    name: '巡检',
    badge: 15,
    jump: '',
  },
  {
    icon: 'icon-maintenance',
    name: '维保',
    badge: 15,
    jump: '/pages/maintenanceRecords/index',
  },
  {
    icon: 'icon-maintenance',
    name: '采购',
    badge: 15,
    jump: '/pages/purchaseOrders/index',
  },
  {
    icon: 'icon-warning',
    name: '异常',
    badge: 15,
    jump: '',
  },
  {
    icon: 'icon-equipment',
    name: '设备',
    badge: 15,
    jump: '/pages/deviceManagement/index',
  },
  {
    icon: 'icon-storage',
    name: '库存',
    badge: 15,
    jump: '',
  },
])

const jumpToPage = url => {
  if (url) {
    uni.navigateTo({
      url,
    })
  }
}

onMounted(() => {
  console.log('页面已加载')
})

onUnmounted(() => {
  console.log('页面已卸载')
})
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  color: #fff;
  overflow: hidden;
}

.info-box {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 0 10rpx;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.top-bar {
  position: absolute;
  top: 60rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  align-items: flex-start;
  z-index: 10;

  .spacer {
    flex-grow: 1;
  }

  .navigation-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;
    }
    .info-label {
      font-size: 28rpx;
      color: #ccc;
      margin-right: 16rpx;
    }
    .info-value {
      font-size: 28rpx;
      font-weight: bold;
      color: #fff;
      margin-right: 32rpx;
    }
    .coordinates {
      font-size: 32rpx;
      color: #ddd;
    }
  }

  .time-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

    .time-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .time-label {
      font-size: 28rpx;
      color: #ccc;
      margin-right: 20rpx;
    }
    .time-value {
      font-size: 28rpx;
      color: #fff;
    }
    .time-row + .time-row {
      margin-top: 12rpx;
    }
  }

  .weather-box {
    display: flex;
    align-items: center;
    height: 100%;

    .weather-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      height: 100%;
      gap: 8rpx;
    }

    .weather-main {
      display: flex;
      align-items: center;
    }
    .weather-icon {
      width: 80rpx;
      height: 80rpx;
      margin-right: 16rpx;
    }
    .temperature {
      font-size: 56rpx;
      font-weight: bold;
    }
    .weather-details {
      display: flex;
      gap: 20rpx;
      align-items: center;
      font-size: 32rpx;
    }
  }

  .connection-status {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    gap: 12rpx;

    .status-item {
      display: flex;
      align-items: center;
      font-size: 32rpx;
    }
    .status-dot {
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      margin-right: 16rpx;
      &.green {
        background-color: #28a745;
      }
    }
  }
}

.right-menu {
  position: absolute;
  top: 50%;
  right: 20rpx;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  padding: 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 75rpx;
    color: #fff;
    cursor: pointer;

    .iconfont {
      font-size: 48rpx;
      margin-bottom: 8rpx;
    }

    .menu-text-row {
      display: flex;
      align-items: center;
      gap: 10rpx;

      text {
        font-size: 36rpx;
      }

      .badge {
        background-color: red;
        color: white;
        border-radius: 50%;
        width: 32rpx;
        height: 32rpx;
        font-size: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }
    }
  }
  .menu-separator {
    width: 80%;
    height: 2rpx;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 20rpx 0;
  }
  .menu-toggle {
    .iconfont {
      font-size: 40rpx;
      color: #fff;
    }
  }
}

.bottom-nav {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
  border-radius: 20rpx;
  overflow: hidden;

  .nav-item {
    padding: 20rpx 50rpx;
    cursor: pointer;
    text {
      font-size: 32rpx;
      color: #ccc;
    }
    &.active {
      background-color: rgba(0, 123, 255, 0.7);
      text {
        color: #fff;
      }
    }
    &:not(:last-child) {
      border-right: 2rpx solid rgba(255, 255, 255, 0.2);
    }
  }
}

.settings-button {
  position: absolute;
  bottom: 40rpx;
  right: 40rpx;
  width: 88rpx;
  height: 88rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  cursor: pointer;

  .iconfont {
    font-size: 48rpx;
    color: #fff;
  }
}
</style>
