<template>
  <view class="normal-login-container">
    <!-- 左上角LOGO -->
    <view class="top-logo">
      <image src="/static/images/login/logo.png" mode="aspectFit"></image>
    </view>

    <!-- 登录区域整体 -->
    <view class="login-form-wrapper">
      <view class="login-content-box">
        <!-- 系统标题 -->
        <view class="system-title">
          <text>船用火灾报警远程运维管理系统</text>
        </view>

        <!-- 登录表单 -->
        <view class="login-form-content">
          <view class="login-title">
            <text class="welcome-text">欢迎登录</text>
            <text class="welcome-en">WELCOME TO LOGIN</text>
          </view>

          <!-- 账号输入框 -->
          <view class="input-item">
            <uni-easyinput v-model="loginForm.username" type="text" :clearable="false" placeholder="请输入您的账号" />
          </view>

          <!-- 密码输入框 -->
          <view class="input-item">
            <uni-easyinput
              v-model="loginForm.password"
              type="password"
              :clearable="false"
              placeholder="请输入您的密码"
            />
          </view>

          <!-- 登录按钮 -->
          <view class="login-btn-wrapper">
            <button @click="handleLogin" class="login-btn">登录</button>
          </view>

          <!-- 记住密码 -->
          <view class="remember-pwd">
            <label @click="rememberPwd = !rememberPwd">
              <checkbox style="margin-right: 10rpx" :checked="rememberPwd" />记住密码
            </label>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 状态变量
const rememberPwd = ref(true)

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

// 简单的加密解密函数 - 使用简单的字符串转换
function encrypt(text) {
  // 简单的字符串反转加上固定字符串作为加密
  return text.split('').reverse().join('') + '_encrypted'
}

function decrypt(encoded) {
  try {
    // 去掉固定字符串后反转回来
    if (encoded.endsWith('_encrypted')) {
      return encoded.slice(0, -10).split('').reverse().join('')
    }
    return encoded
  } catch (e) {
    console.error('解密失败', e)
    return ''
  }
}

// 保存登录凭据
function saveCredentials() {
  if (rememberPwd.value) {
    const credentials = {
      username: loginForm.value.username,
      password: encrypt(loginForm.value.password),
      remember: true,
    }
    uni.setStorageSync('loginCredentials', JSON.stringify(credentials))
  } else {
    // 如果不记住密码，则清除存储的凭据
    uni.removeStorageSync('loginCredentials')
  }
}

// 读取登录凭据
function loadCredentials() {
  try {
    const credentialsStr = uni.getStorageSync('loginCredentials')
    if (credentialsStr) {
      const credentials = JSON.parse(credentialsStr)
      if (credentials && credentials.remember) {
        loginForm.value.username = credentials.username
        loginForm.value.password = decrypt(credentials.password)
        rememberPwd.value = true
      }
    }
  } catch (e) {
    console.error('读取登录凭据失败', e)
  }
}

// 登录方法
async function handleLogin() {
  if (!loginForm.value.username || !loginForm.value.password) {
    return uni.showToast({
      title: '账号和密码不能为空',
      icon: 'none',
    })
  }

  try {
    await userStore.login({ username: loginForm.value.username, password: loginForm.value.password })
    await userStore.getInfo()

    // 保存登录凭据
    saveCredentials()

    proxy.$tab.reLaunch('/pages/index')
  } catch (error) {
    console.error('登录失败', error)
    uni.showToast({
      title: '登录失败，请检查账号密码',
      icon: 'none',
    })
  }
}

// 检查是否已登录
async function checkLogin() {
  // 检查token是否存在
  const token = getToken()
  if (token) {
    try {
      // 获取用户信息
      await userStore.getInfo()
      // 跳转到首页
      proxy.$tab.reLaunch('/pages/index')
      return true
    } catch (error) {
      console.error('自动登录失败，token可能已过期', error)
      // token无效，继续显示登录页
      return false
    }
  }
  return false
}

onLoad(async () => {
  // 先检查是否已登录
  const isLoggedIn = await checkLogin()
  if (!isLoggedIn) {
    // 如果未登录，则尝试加载记住的凭据
    loadCredentials()

    // 开发环境下的默认账号密码
    if (process.env.NODE_ENV === 'development' && !loginForm.value.username) {
      console.log('当前是开发环境')
      loginForm.value.username = 'ry'
      loginForm.value.password = '123456'
    }
  }
})
</script>

<style lang="scss" scoped>
page {
  background-color: #ffffff;
}

.normal-login-container {
  width: 100%;
  height: 100vh;
  background-image: url('/static/images/login/login-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  position: relative;

  .top-logo {
    position: absolute;
    top: 30rpx;
    left: 30rpx;
    width: 180rpx;
    height: 120rpx;
    z-index: 10;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .login-form-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 50rpx;
  }

  .login-content-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 120rpx; /* 为顶部logo留出空间 */
  }

  .system-title {
    width: 100%;
    text-align: center;
    margin-bottom: 30rpx; /* 减少与登录框的距离 */

    text {
      color: #ffffff;
      font-size: 42rpx;
      font-weight: bold;
      letter-spacing: 2rpx;
      text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
      white-space: nowrap; /* 防止文本换行 */
    }
  }

  .login-form-content {
    width: 100%;
    max-width: 650rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);

    .login-title {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 40rpx;

      .welcome-text {
        font-size: 42rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .welcome-en {
        font-size: 26rpx;
        color: #999;
      }
    }

    .input-item {
      position: relative;
      margin-top: 30rpx;

      :deep(.uni-easyinput__content) {
        height: 100rpx;
        line-height: 100rpx;
      }

      :deep(.uni-easyinput__content-input) {
        height: 100rpx;
        line-height: 100rpx;
        font-size: 30rpx;
      }
    }

    .login-btn-wrapper {
      margin-top: 70rpx;

      .login-btn {
        width: 100%;
        height: 100rpx;
        line-height: 100rpx;
        background-color: #4285f4;
        color: #ffffff;
        border-radius: 8rpx;
        font-size: 34rpx;
        font-weight: 500;
        border: none;
        box-shadow: 0 5rpx 15rpx rgba(66, 133, 244, 0.3);
      }
    }

    .remember-pwd {
      margin-top: 30rpx;
      padding-left: 10rpx;

      label {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #666;
      }
    }
  }
}
</style>
