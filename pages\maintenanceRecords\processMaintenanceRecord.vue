<template>
  <view class="container">
    <view class="device-card">
      <view class="device-info">
        <image class="device-icon" src="/static/images/maintenance/device.png" mode="aspectFit"></image>
        <view class="device-details">
          <view class="device-title"
            >{{ formData.deviceName }} <view class="device-type">{{ formData.categoryName }}</view></view
          >
          <view class="device-specs">
            <text class="spec-item">设备型号: {{ formData.model }}</text>
            <text class="spec-item">设备规格: {{ formData.specifications }}</text>
            <text class="spec-item">具体位置: {{ formData.pointName }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="form-container">
      <!-- 处理结果 -->
      <view class="form-item">
        <view class="form-label required">处理结果</view>
        <view class="form-content">
          <view class="button-group">
            <button
              class="action-btn"
              :class="{
                active: formData.processResultType == 1,
                disabled: mode == 'view' && formData.processResultType != 1,
                'active-disabled': mode == 'view' && formData.processResultType == 1,
              }"
              :disabled="mode == 'view'"
              @tap="mode == 'edit' ? selectProcessResult(1) : null"
            >
              处理
            </button>
            <button
              class="action-btn"
              :class="{
                active: formData.processResultType == 0 && formData.processResultType != null,
                disabled: mode == 'view' && formData.processResultType != 0,
                'active-disabled': mode == 'view' && formData.processResultType == 0,
              }"
              :disabled="mode == 'view'"
              @tap="mode == 'edit' ? selectProcessResult(0) : null"
            >
              不处理
            </button>
          </view>
        </view>
      </view>

      <!-- 设备编号 -->
      <view v-if="!shouldHideFields" class="form-item">
        <view class="form-label required">设备编号</view>
        <view class="form-content">
          <input
            class="form-input"
            v-model="formData.code"
            :placeholder="mode == 'view' ? '' : '请输入'"
            :readonly="mode == 'view'"
            :disabled="mode == 'view'"
          />
        </view>
      </view>

      <!-- 证书编号 -->
      <view v-if="!shouldHideFields" class="form-item">
        <view class="form-label">证书编号</view>
        <view class="form-content">
          <input
            class="form-input"
            v-model="formData.certificateNumber"
            :placeholder="mode == 'view' ? '' : '请输入'"
            :readonly="mode == 'view'"
            :disabled="mode == 'view'"
          />
        </view>
      </view>

      <!-- 证书照片 -->
      <view v-if="!shouldHideFields" class="form-item photo-item-container">
        <view class="form-label">证书照片</view>
        <view class="photo-list">
          <view class="photo-item" v-for="(photo, index) in formData.certificatePhotos" :key="index">
            <image
              class="photo-image"
              :src="config.baseUrl + photo"
              mode="aspectFill"
              @tap="previewPhoto(formData.certificatePhotos, index)"
            ></image>
            <view v-if="mode == 'edit'" class="photo-delete" @tap="deletePhoto('certificatePhotos', index)">×</view>
          </view>
          <view v-if="mode == 'edit'" class="photo-add" @tap="addPhoto('certificatePhotos')">
            <image class="add-icon" src="/static/images/maintenance/camera.png"></image>
            <text class="add-text">添加照片</text>
          </view>
        </view>
      </view>

      <!-- 处理结果描述 -->
      <view class="form-item">
        <view class="form-label required">处理结果描述</view>
        <view class="form-content">
          <input
            class="form-input"
            v-model="formData.processResult"
            :placeholder="mode == 'view' ? '' : '请输入'"
            :readonly="mode == 'view'"
            :disabled="mode == 'view'"
          />
        </view>
      </view>

      <!-- 处理结果照片 -->
      <view class="form-item photo-item-container">
        <view class="form-label required">处理结果照片</view>
        <view class="photo-list">
          <view class="photo-item" v-for="(photo, index) in formData.processPhotos" :key="index">
            <image
              class="photo-image"
              :src="config.baseUrl + photo"
              mode="aspectFill"
              @tap="previewPhoto(formData.processPhotos, index)"
            ></image>
            <view v-if="mode == 'edit'" class="photo-delete" @tap="deletePhoto('processPhotos', index)">×</view>
          </view>
          <view v-if="mode == 'edit'" class="photo-add" @tap="addPhoto('processPhotos')">
            <image class="add-icon" src="/static/images/maintenance/camera.png"></image>
            <text class="add-text">添加照片</text>
          </view>
        </view>
      </view>
    </view>

    <view v-if="mode == 'edit'" class="bottom-bar">
      <button class="confirm-btn" @tap="submitForm">确定</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { processSingleDevice } from '@/api/maintenance'
import upload from '@/utils/upload'
import config from '@/config'

const { proxy } = getCurrentInstance()

const mode = ref('view') // 页面模式：view-查看 edit-编辑
const orderType = ref(0) // 订单类型：0 新增 1 更换 2 移除

// 计算属性：是否应该隐藏设备编号、证书编号、证书照片等字段
const shouldHideFields = computed(() => {
  const result = orderType.value == 2 || formData.value.processResultType == 0
  return result
})

// 表单数据
const formData = ref({
  // 设备基本信息（从上一页传入，用于显示）
  deviceName: '', // 设备名称
  categoryName: '', // 设备类型
  model: '', // 设备型号
  specifications: '', // 设备规格
  pointName: '', // 具体位置

  // 提交字段（与后端接口对应）
  id: '', // 维保订单详情id
  code: '', // 设备编号
  certificateNumber: '', // 证书编号
  certificate: '', // 证书照片
  processResult: '', // 处理结果描述
  processResultImg: '', // 处理结果照片
  processResultType: '', // 处理结果类型：0-不处理, 1-处理

  // 前端辅助字段（不提交）
  certificatePhotos: [], // 证书照片数组（用于界面显示）
  processPhotos: [], // 处理结果照片数组（用于界面显示）
})

// 选择处理结果
const selectProcessResult = result => {
  formData.value.processResultType = result
}

// 添加照片
const addPhoto = type => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: res => {
      let uploadData = new FormData()
      uploadData.append('file', res.tempFiles[0])
      upload({
        url: '/upload',
        filePath: res.tempFiles[0].path,
        formData: uploadData,
      }).then(res => {
        formData.value[type].push(res.fileName)
      })
    },
  })
}

// 删除照片
const deletePhoto = (type, index) => {
  formData.value[type].splice(index, 1)
}

// 预览照片
const previewPhoto = (photos, currentIndex) => {
  const urls = photos.map(photo => config.baseUrl + photo)
  uni.previewImage({
    current: currentIndex,
    urls: urls,
  })
}

// 表单验证
const validateForm = () => {
  console.log(formData.value.processResultType)
  if (
    formData.value.processResultType === '' ||
    formData.value.processResultType === null ||
    formData.value.processResultType === undefined
  ) {
    uni.showToast({
      title: '请选择处理结果',
      icon: 'none',
    })
    return false
  }

  // 当需要隐藏字段时，不验证设备编号
  if (!formData.value.code && !shouldHideFields.value) {
    uni.showToast({
      title: '请输入设备编号',
      icon: 'none',
    })
    return false
  }

  if (!formData.value.processResult) {
    uni.showToast({
      title: '请输入处理结果描述',
      icon: 'none',
    })
    return false
  }

  if (formData.value.processPhotos.length === 0) {
    uni.showToast({
      title: '请添加处理结果照片',
      icon: 'none',
    })
    return false
  }

  return true
}

// 提交表单
const submitForm = () => {
  if (!validateForm()) return

  proxy.$modal.loading('提交中...')

  // 准备提交数据，只包含后端需要的字段
  const submitData = {
    id: formData.value.id, // 维保订单详情id
    code: formData.value.code, // 设备编号
    certificateNumber: formData.value.certificateNumber, // 证书编号
    certificate: formData.value.certificatePhotos.join(','), // 证书照片
    processResult: formData.value.processResult, // 处理结果描述
    processResultImg: formData.value.processPhotos.join(','), // 处理结果照片
    processResultType: formData.value.processResultType, // 处理结果类型
  }

  processSingleDevice(submitData)
    .then(res => {
      proxy.$modal.msgSuccess('提交成功')

      // 刷新列表数据

      setTimeout(() => {
        uni.$emit('refreshMaintenanceDetail')
        uni.navigateBack()
      }, 1000)
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

// 接收设备数据并初始化表单
onLoad(option => {
  mode.value = option.mode ?? 'view'
  orderType.value = parseInt(option.orderType) || 0

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: mode.value == 'view' ? '查看处理记录' : '处理记录',
  })

  const deviceData = uni.getStorageSync('currentDeviceItem')

  if (deviceData) {
    // 映射设备基本信息到表单
    Object.assign(formData.value, {
      // 显示字段
      deviceName: deviceData.deviceName || '',
      categoryName: deviceData.categoryName || '',
      model: deviceData.model || '',
      specifications: deviceData.specifications || '',
      pointName: deviceData.pointName || '',

      // 提交字段
      id: deviceData.id || '', // 维保订单详情id
      code: deviceData.code || '',
      certificateNumber: deviceData.certificateNumber || '',
      certificate: deviceData.certificate || '',
      processResult: deviceData.processResult || '',
      processResultImg: deviceData.processResultImg || '',
      processResultType: deviceData.processResultType === null ? '' : deviceData.processResultType,
    })

    // 初始化照片数组（用于界面显示）
    if (deviceData.certificate) {
      formData.value.certificatePhotos = deviceData.certificate.split(',').filter(url => url.trim())
    }
    if (deviceData.processResultImg) {
      formData.value.processPhotos = deviceData.processResultImg.split(',').filter(url => url.trim())
    }
  }

  // 清除存储的数据
  uni.removeStorageSync('currentDeviceItem')
})
</script>

<style lang="scss" scoped>
.container {
  height: $uni-height-area;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
  padding-top: 30rpx;
}

.device-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  border-radius: 8rpx;
}

.device-info {
  height: 125rpx;
  display: flex;
  align-items: center;
}

.device-icon {
  width: 125rpx;
  height: 125rpx;
  margin-right: 20rpx;
}

.device-details {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.device-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #000;
  display: flex;
  align-items: center;
}

.device-type {
  font-size: 24rpx;
  color: #2979ff;
  background-color: #e8f4ff;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.device-specs {
  display: flex;
}

.spec-item {
  font-size: 26rpx;
  color: #666;
  line-height: 36rpx;
  margin-right: 20rpx;
}

.form-container {
  flex: 1;
  margin-left: 30rpx;
  margin-right: 30rpx;
  border-radius: 8rpx;
}

.form-item {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.photo-item-container {
  flex-direction: column;
  align-items: flex-start;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  min-width: 200rpx;
  position: relative;

  &.required:before {
    content: '*';
    color: #ff4d4f;
    margin-right: 6rpx;
  }
}

.form-content {
  flex: 1;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #fff;
  color: #333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    border-color: #2979ff;
    background-color: #f0f8ff;
    color: #2979ff;
  }

  &.disabled {
    background-color: #f5f5f5;
    color: #ccc;
    border-color: #e5e5e5;
    cursor: not-allowed;
  }

  &.active-disabled {
    border-color: #2979ff;
    background-color: #f0f8ff;
    color: #2979ff;
    cursor: not-allowed;
  }
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: transparent;
  box-sizing: border-box;
  text-align: right;

  &:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
  width: 100%;
}

.photo-item {
  position: relative;
  width: 140rpx;
  height: 140rpx;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  object-fit: cover;
}

.photo-delete {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-add {
  width: 140rpx;
  height: 140rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f8fa;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.add-text {
  font-size: 22rpx;
  color: #999;
}

.bottom-bar {
  padding: 20rpx;
  background-color: #fff;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background-color: #2979ff;
  border-radius: 8rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
