<template>
  <CustomPopup
    title="筛选条件"
    :modelValue="visible"
    @update:modelValue="updateVisible"
    @confirm="handleConfirm"
    @reset="handleReset"
    resetText="重置筛选条件"
    confirmText="确定"
  >
    <!-- 订单编号 -->
    <view class="filter-item">
      <text class="filter-label">订单编号</text>
      <view class="filter-input-container">
        <input
          type="text"
          v-model="filterParams.orderCode"
          class="filter-input"
          placeholder="请输入采购订单号"
          placeholder-class="placeholder"
        />
      </view>
    </view>

    <!-- 开始时间 -->
    <view class="filter-item">
      <text class="filter-label">开始时间</text>
      <uni-datetime-picker
        type="datetimerange"
        v-model="startTimeRange"
        :clear-icon="true"
        placeholder="请选择开始时间范围"
        hide-second
        @change="handleStartTimeRangeChange"
      />
    </view>

    <!-- 结束时间 -->
    <view class="filter-item">
      <text class="filter-label">结束时间</text>
      <uni-datetime-picker
        type="datetimerange"
        v-model="endTimeRange"
        :clear-icon="true"
        placeholder="请选择结束时间范围"
        hide-second
        @change="handleEndTimeRangeChange"
      />
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref, reactive, computed, watch, getCurrentInstance, onMounted } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'

// 获取当前实例上下文
const { proxy } = getCurrentInstance()

const props = defineProps({
  // 控制弹窗显示状态
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 初始筛选参数
  defaultParams: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 弹窗可见性
const visible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 更新弹窗可见性
const updateVisible = val => {
  visible.value = val
}

// 日期范围值
const startTimeRange = ref([])
const endTimeRange = ref([])

// 筛选参数
const filterParams = reactive({
  orderCode: '', // 采购单号
  startTimeStart: '', // 开始时间-开始
  startTimeEnd: '', // 开始时间-结束
  endTimeStart: '', // 结束时间-开始
  endTimeEnd: '', // 结束时间-结束
})

// 处理开始时间范围变化
const handleStartTimeRangeChange = e => {
  if (e && Array.isArray(e) && e.length === 2) {
    // 确保日期时间格式包含秒
    filterParams.startTimeStart = appendSeconds(e[0])
    filterParams.startTimeEnd = appendSeconds(e[1])
  } else {
    filterParams.startTimeStart = ''
    filterParams.startTimeEnd = ''
  }
}

// 处理结束时间范围变化
const handleEndTimeRangeChange = e => {
  if (e && Array.isArray(e) && e.length === 2) {
    // 确保日期时间格式包含秒
    filterParams.endTimeStart = appendSeconds(e[0])
    filterParams.endTimeEnd = appendSeconds(e[1])
  } else {
    filterParams.endTimeStart = ''
    filterParams.endTimeEnd = ''
  }
}

// 添加秒到日期时间字符串
const appendSeconds = dateTimeStr => {
  if (!dateTimeStr) return ''

  // 如果日期时间字符串已经包含秒，则直接返回
  if (dateTimeStr.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    return dateTimeStr
  }

  // 如果日期时间字符串格式为 yyyy-MM-dd HH:mm，则添加 :00
  if (dateTimeStr.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/)) {
    return `${dateTimeStr}:00`
  }

  return dateTimeStr
}

// 设置类型
const setType = type => {
  if (filterParams.type === type) {
    filterParams.type = ''
  } else {
    filterParams.type = type
  }
}

// 确认筛选
const handleConfirm = () => {
  emit('confirm', { ...filterParams })
}

// 重置筛选
const handleReset = () => {
  // 重置所有筛选参数
  filterParams.orderCode = ''
  filterParams.startTimeStart = ''
  filterParams.startTimeEnd = ''
  filterParams.endTimeStart = ''
  filterParams.endTimeEnd = ''

  // 重置日期范围选择器的值
  startTimeRange.value = []
  endTimeRange.value = []
}

// 初始化数据
const initData = () => {
  if (props.defaultParams) {
    // 合并默认参数
    Object.assign(filterParams, props.defaultParams)

    // 初始化日期范围
    if (filterParams.startTimeStart && filterParams.startTimeEnd) {
      startTimeRange.value = [filterParams.startTimeStart, filterParams.startTimeEnd]
    }

    if (filterParams.endTimeStart && filterParams.endTimeEnd) {
      endTimeRange.value = [filterParams.endTimeStart, filterParams.endTimeEnd]
    }
  }
}

// 初始化
initData()
</script>

<style lang="scss" scoped>
.filter-item {
  margin-bottom: 30rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-input-container {
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
}

.filter-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

.filter-types {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.type-item {
  margin: 10rpx;
  padding: 0 30rpx;
  height: 70rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;

  &.active {
    border-color: #2979ff;
    background-color: rgba(41, 121, 255, 0.1);
    color: #2979ff;
  }
}

.time-range {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 20rpx;
  color: #999;
}

.datetime-picker {
  flex: 1;
}
</style>
