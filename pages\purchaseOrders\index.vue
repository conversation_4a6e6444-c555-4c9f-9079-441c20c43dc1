<template>
  <view class="purchase-orders-container">
    <TabFilter
      v-model="searchParams.state"
      :tabs="tabs"
      @tab-change="handleTabChange"
      @filter-click="handleFilterClick"
    ></TabFilter>
    <view class="content-container">
      <CustomSwipeCard
        class="record-item"
        v-if="recordsList.length > 0"
        v-for="(record, index) in recordsList"
        :key="record.id"
        :enable-swipe="record.state != 2 && index == 0"
        :currentData="record"
        :customSwipeOptions="getSwipeOptions(record.state)"
        @swipe-click="handleRecordClick"
        @card-click="handleRecordDetail(record)"
      >
        <template #content>
          <view class="record-item-content">
            <view v-if="getStatusImage(record.state)" class="order-status-container">
              <image class="order-status-tag" :src="getStatusImage(record.state)" mode="scaleToFill" />
              <text class="order-status-text">{{ record.stateName }}</text>
            </view>
            <view class="title">
              <image src="/static/images/maintenance/green.png" mode="scaleToFill" />
              <view class="title-text">{{ record.state == 3 ? '采购' : `采购 - ${record.orderCode}` }}</view>
            </view>
            <view class="detail">
              <view class="detail-item">
                <label class="label">开始时间</label>
                <text class="value">{{ record.startTime || '--' }}</text>
              </view>
              <view class="detail-item" v-if="record.state == 3 || record.state == 0">
                <label class="label">采购期限</label>
                <text class="value">{{ formatDuration(record.maintenPeriod) || '--' }}</text>
              </view>
              <view class="detail-item" v-if="record.state != 3 && record.state != 0">
                <label class="label">结束时间</label>
                <text class="value">{{ record.endTime || '--' }}</text>
              </view>
              <view class="detail-item" v-if="record.state == 1 || record.state == 0">
                <label class="label">超时时间</label>
                <text class="value">{{ record.timeOut || '--' }}</text>
              </view>
              <view class="detail-item">
                <label class="label">紧急程度</label>
                <text class="value" :class="getUrgencyClass(record.maintenOrderUrgencyName)">{{
                  record.maintenOrderUrgencyName || '--'
                }}</text>
              </view>
            </view>
          </view>
        </template>
      </CustomSwipeCard>
      <EmptyData v-else text="暂无数据" />
    </view>
    <view class="btn-container">
      <button class="add-btn" @tap="handleAddBtn">填写采购订单</button>
    </view>

    <!-- 添加备注弹窗组件 -->
    <CommonRemarksPopup ref="remarksPopupRef" @success="handleRemarksSuccess" />
    <!-- 作废弹窗组件 -->
    <CommonRemarksPopup ref="cancelPopupRef" title="作废" placeholder="请输入作废原因" @success="handleCancelSuccess" />
  </view>
  <!-- 筛选弹窗 -->
  <FilterPopup v-model="showFilterPopup" :defaultParams="searchParams" @confirm="handleFilterConfirm" />
</template>
<script setup>
import { ref, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import TabFilter from '@/components/TabFilter/TabFilter.vue'
import CustomSwipeCard from '@/components/CustomSwipeCard/CustomSwipeCard.vue'
import EmptyData from '@/components/EmptyData/EmptyData.vue'
import FilterPopup from './components/filterPopup.vue'
import { getMaintenanceRecords, deleteDraft, addRemarks, cancelMaintenanceRecord } from '@/api/maintenance'
import { formatDuration } from '@/utils/formatUtils'
// 导入备注弹窗组件
import CommonRemarksPopup from '@/components/CommonRemarksPopup/CommonRemarksPopup.vue'

const { proxy } = getCurrentInstance()

// 滑动选项配置
const configMap = {
  // 未完成
  0: [
    {
      text: '处理',
      style: {
        backgroundColor: '#27a376',
      },
    },
    {
      text: '作废',
      style: {
        backgroundColor: '#dd524d',
      },
    },
    {
      text: '备注',
      style: {
        backgroundColor: '#007aff',
      },
    },
  ],
  // 已完成
  1: [
    {
      text: '备注',
      style: {
        backgroundColor: '#007aff',
      },
    },
  ],
  // 已作废
  2: [],
  // 草稿箱
  3: [
    {
      text: '填写',
      style: {
        backgroundColor: '#2979ff',
      },
    },
    {
      text: '删除',
      style: {
        backgroundColor: '#dd524d',
      },
    },
  ],
}

// 状态图片映射
const statusImageMap = {
  0: '/static/images/error-tag.png',
  1: '/static/images/success-tag.png',
  2: '/static/images/warning-tag.png',
}

const tabs = ref([
  { name: '草稿箱', value: 3 },
  { name: '未完成', value: 0 },
  { name: '历史', value: '1,2' },
])

const searchParams = ref({
  startTimeEnd: '', // 开始时间 开始
  startTimeStart: '', // 开始时间  结束
  endTimeStart: '', // 结束时间  开始
  endTimeEnd: '', // 结束时间  结束
  orderCode: '', // 采购单号
  state: 0, // 0:未完成 1:已完成 2:已作废 3:草稿箱
  type: '3', // 只有采购
  mark: 1, // 0是非采购类 1是采购类
})

const recordsList = ref([])
const showFilterPopup = ref(false) // 控制筛选弹窗显示

// 备注弹窗引用
const remarksPopupRef = ref(null)
const cancelPopupRef = ref(null)

// 备注提交成功回调
function handleRemarksSuccess(res) {
  // 刷新列表
  getRecordsList()
}

// 作废提交成功回调
function handleCancelSuccess(res) {
  // 刷新列表
  getRecordsList()
}

// 根据当前状态获取对应的滑动操作按钮
function getSwipeOptions(state) {
  return configMap[Number(state)] || []
}

// 根据状态获取对应的状态图片
function getStatusImage(state) {
  return statusImageMap[Number(state)] || ''
}

// 根据紧急程度获取对应的样式类
function getUrgencyClass(urgency) {
  const classMap = {
    一级: 'urgency-high',
    二级: 'urgency-medium',
    三级: 'urgency-low',
  }
  return classMap[urgency] || ''
}

function handleRecordDetail(record) {
  // 如果状态为草稿箱则直接返回，不进行跳转
  if (record.state == 3) return
  proxy.$tab.navigateTo(`/pages/purchaseOrders/orderDetail?recordId=${record.id}&mode=view`)
}

function handleRecordClick(e) {
  const {
    index,
    option: { text },
    currentData,
  } = e

  const actionHandlers = {
    处理: () => {
      proxy.$tab.navigateTo(`/pages/purchaseOrders/orderDetail?recordId=${currentData.id}&mode=edit`)
    },
    作废: () => {
      cancelPopupRef.value.open({
        id: currentData.id,
        submitFunc: data => {
          // 调用API提交备注
          return cancelMaintenanceRecord({
            id: data.id,
            remark: data.remark,
          })
        },
      })
    },
    备注: () => {
      // 打开备注弹窗
      remarksPopupRef.value.open({
        id: currentData.id,
        submitFunc: data => {
          // 调用API提交备注
          return addRemarks({
            id: data.id,
            remark: data.remark,
          })
        },
      })
    },
    填写: () => {
      proxy.$tab.navigateTo(`/pages/purchaseOrders/addPurchaseOrder?id=${currentData.id}&type=Draft`)
    },
    删除: () => {
      // 删除逻辑
      proxy.$modal.confirm('确定要删除该采购记录吗？').then(res => {
        if (res.confirm) {
          proxy.$modal.loading('删除中...')
          // 调用删除API
          deleteDraft({ id: currentData.id })
            .then(() => {
              proxy.$modal.msgSuccess('删除成功')
              getRecordsList()
            })
            .finally(() => {
              proxy.$modal.closeLoading()
            })
        }
      })
    },
  }

  // 执行对应的处理函数，如果不存在则提示不支持的操作
  if (actionHandlers[text]) {
    actionHandlers[text]()
  } else {
    proxy.$modal.msg(`不支持的操作: ${text}`)
  }
}

function handleTabChange() {
  getRecordsList()
}

// 处理筛选按钮点击事件
function handleFilterClick() {
  showFilterPopup.value = true
}

// 处理筛选确认
function handleFilterConfirm(params) {
  // 更新搜索参数
  Object.assign(searchParams.value, params)
  // 重新获取数据
  getRecordsList()
}

// 处理添加按钮点击事件
function handleAddBtn() {
  proxy.$tab.navigateTo('/pages/purchaseOrders/addPurchaseOrder')
}

async function getRecordsList() {
  try {
    proxy.$modal.loading('加载中...')
    // 获取采购记录列表
    const res = await getMaintenanceRecords(searchParams.value)
    recordsList.value = res.data
  } catch (error) {
    console.error('获取采购记录失败:', error)
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 页面加载
onMounted(() => {
  getRecordsList()

  // 监听刷新列表事件
  uni.$on('refreshPurchaseList', data => {
    // 刷新列表数据
    getRecordsList()
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshPurchaseList')
})
</script>
<style lang="scss" scoped>
.purchase-orders-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
}

.content-container {
  position: relative;
  flex: 1;
  overflow-y: scroll;
  min-height: 0;
  padding: 0 30rpx;
  margin-top: 30rpx;
  background-color: #f5f5f5;

  .record-item {
    margin-bottom: 30rpx;
  }
}

.record-item-content {
  padding: 54rpx 0 42rpx 64rpx;
  position: relative;

  .order-status-container {
    position: absolute;
    display: inline-flex;
    top: 0;
    right: 0;

    .order-status-tag {
      width: 170rpx;
      height: 66rpx;
    }

    .order-status-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 28rpx;
      font-weight: 500;
      color: #fff;
    }
  }

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 50rpx;

    image {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }

    .title-text {
      font-size: 38rpx;
      font-weight: 500;
      color: #000;
    }
  }

  .detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    .detail-item {
      .label {
        color: #7d7d91;
        font-size: 32rpx;
        font-weight: 400;
        margin-right: 20rpx;
      }

      .value {
        color: #222631;
        font-size: 32rpx;
        font-weight: 400;

        &.urgency-high {
          color: #ce4427;
        }

        &.urgency-medium {
          color: #ea9d4a;
        }

        &.urgency-low {
          color: #4a8ce6;
        }
      }
    }
  }
}

.btn-container {
  padding: 30rpx;

  .add-btn {
    background-color: #2979ff;
    color: #fff;
    font-size: 36rpx;
    font-weight: 500;
  }
}
</style>
