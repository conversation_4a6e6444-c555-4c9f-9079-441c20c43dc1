<template>
  <view class="record-detail-container">
    <!-- 标签页 -->
    <CustomTab :tabs="tabList" v-model="activeTabIndex" style-type="text" />

    <!-- 基本信息区域 -->
    <scroll-view scroll-y class="content-area" v-if="activeTabIndex == 0">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">
          <view class="title-icon"></view>
          <text>基本信息</text>
        </view>

        <view class="info-item">
          <text class="item-label">订单编号</text>
          <text class="item-value">{{ recordData.orderCode || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">订单状态</text>
          <text class="item-value">{{ recordData.stateName || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">紧急程度</text>
          <text class="item-value">{{ recordData.maintenOrderUrgencyName || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">订单类型</text>
          <text class="item-value">采购</text>
        </view>

        <view class="info-item">
          <text class="item-label">采购期限</text>
          <text class="item-value">{{ formatDuration(recordData.maintenPeriod) || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">开始时间</text>
          <text class="item-value">{{ recordData.startTime || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">超时时间</text>
          <text class="item-value">{{ recordData.timeOut || '--' }}</text>
        </view>
      </view>

      <!-- 处理结果区域 - 仅在已完成或已作废状态显示 -->
      <block v-if="isCompleted">
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>处理结果</text>
          </view>
          <view class="result-content">
            <text>{{ recordData.processResult || '暂无处理结果' }}</text>
          </view>
        </view>

        <!-- 作废原因区域 - 仅在已作废状态显示 -->
        <view class="info-section" v-if="recordData.state == 2">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>作废原因</text>
          </view>
          <view class="result-content">
            <text>{{ recordData.cancel || '暂无作废原因' }}</text>
          </view>
        </view>

        <!-- 备注信息区域 -->
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>备注信息</text>
            <!-- <view class="edit-btn" @tap="editRemark">编辑</view> -->
          </view>
          <view class="result-content">
            <text>{{ recordData.remark || '暂无备注信息' }}</text>
          </view>
        </view>
      </block>
    </scroll-view>

    <!-- 设备清单区域 -->
    <scroll-view scroll-y class="content-area" v-if="activeTabIndex == 1">
      <view class="device-grid">
        <view
          class="device-card"
          v-for="(item, index) in recordData.deviceList"
          :key="index"
          @tap="handleDeviceClick(item)"
        >
          <image class="device-image" src="/static/images/maintenance/device.png" mode="aspectFit"></image>
          <view class="device-info">
            <view class="device-code">{{ item.deviceName }}</view>
            <view class="device-serial">
              <text class="purchase-info">采购: {{ item.purchaseNumber }}</text>
              <text class="store-info">已入库: {{ item.sumNumber || 0 }}</text>
            </view>
          </view>
          <view
            class="device-status-tag"
            :class="{
              'status-filled': isDeviceProcessed(item),
              'status-unfilled': !isDeviceProcessed(item),
            }"
          >
            {{ isDeviceProcessed(item) ? '已处理' : '未处理' }}
          </view>
        </view>
      </view>
      <EmptyData v-if="recordData.deviceList.length == 0" text="暂无设备数据" />
    </scroll-view>

    <view v-if="mode == 'edit' && activeTabIndex == 1" class="submit-button-container">
      <button hover-class="button-hover" @tap="handleProcess">提交处理结果</button>
    </view>

    <!-- 基于 uni-popup-dialog 的输入弹窗组件 -->
    <UniInputDialog />
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomTab from '@/components/CustomTab/CustomTab.vue'
import EmptyData from '@/components/EmptyData/EmptyData.vue'
import UniInputDialog from '@/components/UniInputDialog/UniInputDialog.vue'
import { getMaintenanceRecordDetail, submitProcessResult } from '@/api/maintenance'
import { formatDuration } from '@/utils/formatUtils'

const { proxy } = getCurrentInstance()

// 页面参数
const recordId = ref('')
const mode = ref('view') // 页面模式：view-查看 edit-编辑

// 标签页数据
const tabList = ref(['基本信息', '设备清单'])

// 当前激活的标签页索引
const activeTabIndex = ref(0)

// 记录详情数据
const recordData = ref({
  orderCode: '',
  stateName: '',
  maintenOrderUrgencyName: '',
  typeName: '',
  maintenPeriod: '',
  startTime: '',
  timeOut: '',
  processResult: '',
  cancel: '',
  remark: '',
  deviceList: [],
  state: 0,
  type: 0,
})

// 是否可以提交（所有设备都已处理完成）
const canSubmit = computed(() => {
  // 判断是否还有未处理的设备
  let hasUnprocessedDevices = recordData.value.deviceList.some(item => !isDeviceProcessed(item))
  // 有设备且所有设备都已处理完成时才能提交
  return recordData.value.deviceList.length > 0 && !hasUnprocessedDevices
})

// 是否已完成（已完成或已作废状态）
const isCompleted = computed(() => {
  return recordData.value.state == 1 || recordData.value.state == 2
})

async function handleProcess() {
  if (!canSubmit.value) {
    return proxy.$modal.msg('当前还存在未处理的设备，请先全部处理完成后再提交！')
  }

  const submitRemark = await proxy.$modal.promptInput('提交确认', '请填写提交说明', '')

  // 用户确认后提交处理结果
  proxy.$modal.loading('提交中...')

  const submitData = {
    id: recordData.value.id,
    processResult: submitRemark,
  }

  submitProcessResult(submitData)
    .then(res => {
      proxy.$modal.msgSuccess('提交成功')
      // 触发列表刷新
      uni.$emit('refreshPurchaseList')
      setTimeout(() => {
        proxy.$tab.navigateBack()
      }, 0)
    })
    .catch(error => {
      proxy.$modal.msgError(error.message || '提交失败，请重试')
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

// 判断设备是否已处理
function isDeviceProcessed(item) {
  // 当 sumNumber >= purchaseNumber 时为已处理
  // 但是 sumNumber 可能为 null，所以需要特殊处理
  if (item.sumNumber == null) {
    return false
  }
  return item.sumNumber >= item.purchaseNumber
}

// 获取记录详情
async function getRecordDetail() {
  try {
    proxy.$modal.loading('加载中...')
    const res = await getMaintenanceRecordDetail({ id: recordId.value })
    recordData.value = res.data
  } catch (error) {
    proxy.$modal.msgError('获取详情失败')
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 处理设备点击事件
function handleDeviceClick(item) {
  uni.setStorageSync('currentDeviceItem', item)
  proxy.$tab.navigateTo(
    `/pages/purchaseOrders/processPurchaseOrder?mode=${mode.value}&orderType=${recordData.value.type}`
  )
}

// 页面加载
onLoad(option => {
  recordId.value = option.recordId
  mode.value = option.mode ?? 'view'

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: mode.value == 'view' ? '查看采购记录详情' : '处理采购记录',
  })
  getRecordDetail()
})

// 页面加载
onMounted(() => {
  // 监听刷新列表事件
  uni.$on('refreshPurchaseDetail', data => {
    // 刷新数据
    getRecordDetail()
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshPurchaseDetail')
})
</script>

<style lang="scss" scoped>
.record-detail-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
}

.content-area {
  flex: 1;
  padding: 20rpx;
  background-color: transparent;
}

.info-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;

  .title-icon {
    width: 8rpx;
    height: 32rpx;
    background-color: #2979ff;
    margin-right: 20rpx;
    border-radius: 4rpx;
  }

  text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .edit-btn {
    position: absolute;
    right: 0;
    font-size: 28rpx;
    color: #2979ff;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.item-label {
  font-size: 28rpx;
  color: #666;
}

.item-value {
  font-size: 28rpx;
  color: #333;
}

.result-content {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 设备清单样式 */
.device-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  width: 100%;
}

.device-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 28rpx 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
}

.device-image {
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  object-fit: contain;
  flex-shrink: 0;
}

.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.device-code {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.device-serial {
  font-size: 24rpx;
  color: #999;
  display: flex;
  gap: 20rpx;
  margin-top: 4rpx;
}

.purchase-info,
.store-info {
  font-size: 24rpx;
  color: #666;
}

.device-status-tag {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-top-right-radius: 16rpx;
  border-bottom-left-radius: 16rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-weight: 400;

  &.status-filled {
    background-image: url('/static/images/maintenance/grey-bg.png');
    color: #ffffff;
  }

  &.status-unfilled {
    background-image: url('/static/images/maintenance/blue-bg.png');
    color: #ffffff;
  }
}

.submit-button-container {
  width: 100%;
  padding: 0 30rpx 30rpx;

  button {
    background-color: #2979ff;
    color: #fff;
  }
}
</style>
