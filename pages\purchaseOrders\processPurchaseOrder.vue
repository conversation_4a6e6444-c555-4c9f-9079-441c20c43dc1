<template>
  <view class="container">
    <view class="device-card">
      <view class="device-info">
        <image class="device-icon" src="/static/images/maintenance/device.png" mode="aspectFit"></image>
        <view class="device-details">
          <view class="device-title"
            >{{ formData.deviceName }} <view class="device-type">{{ formData.categoryName }}</view></view
          >
          <view class="device-specs">
            <text class="spec-item">设备型号: {{ formData.model }}</text>
            <text class="spec-item">设备规格: {{ formData.specifications }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="form-container">
      <!-- 计划采购数量 -->
      <view class="form-item">
        <view class="form-label">计划采购数量</view>
        <view class="form-content">
          <text class="form-display-text">{{ formData.purchaseNumber }}</text>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">已入库数量</view>
        <view class="form-content">
          <text class="form-display-text">{{ formData.sumNumber }}</text>
        </view>
      </view>

      <!-- 实际入库数量 -->
      <view class="form-item" v-if="mode == 'edit'">
        <view class="form-label required">本次入库数量</view>
        <view class="form-content">
          <uni-number-box v-if="mode == 'edit'" v-model="formData.storeNumber" :min="0" class="number-box-right" />
          <text v-else class="form-display-text">{{ formData.storeNumber }}</text>
        </view>
      </view>
    </view>

    <view v-if="mode == 'edit'" class="bottom-bar">
      <button class="confirm-btn" @tap="submitForm">确定</button>
    </view>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { processSingleDevice } from '@/api/maintenance'

const { proxy } = getCurrentInstance()

const mode = ref('view') // 页面模式：view-查看 edit-编辑

// 表单数据
const formData = ref({
  id: '',
  deviceName: '',
  categoryName: '',
  model: '',
  specifications: '',
  purchaseNumber: 0, // 计划采购数量
  sumNumber: 0, // 已入库数量
  storeNumber: 0, // 本次入库数量
})

// 表单验证
const validateForm = () => {
  if (formData.value.storeNumber < 0) {
    proxy.$modal.msg('实际入库数量不能为负数')
    return false
  }

  return true
}

// 提交表单
const submitForm = async () => {
  if (!validateForm()) return

  try {
    proxy.$modal.loading('提交中...')

    // 准备提交数据，只包含两个核心字段
    const submitData = {
      id: formData.value.id, // 采购订单详情id
      storeNumber: formData.value.storeNumber, // 实际入库数量
    }

    await processSingleDevice(submitData)

    proxy.$modal.msgSuccess('提交成功')

    // 触发列表刷新

    // 返回上一页
    setTimeout(() => {
      uni.$emit('refreshPurchaseDetail')
      proxy.$tab.navigateBack()
    }, 1000)
  } catch (error) {
    proxy.$modal.msgError(error.message || '提交失败，请重试')
  } finally {
    proxy.$modal.closeLoading()
  }
}

onLoad(option => {
  mode.value = option.mode ?? 'view'

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: mode.value == 'view' ? '查看采购记录' : '处理采购记录',
  })

  const deviceData = uni.getStorageSync('currentDeviceItem')

  if (deviceData) {
    // 映射设备基本信息到表单
    Object.assign(formData.value, {
      // 显示字段
      deviceName: deviceData.deviceName || '--',
      categoryName: deviceData.categoryName || '--',
      model: deviceData.model || '--',
      specifications: deviceData.specifications || '--',

      // 提交字段
      id: deviceData.id || '', // 采购订单详情id
      purchaseNumber: deviceData.purchaseNumber, // 计划采购数量
      sumNumber: deviceData.sumNumber || 0, // 已入库数量
    })
  }

  // 清除存储的数据
  uni.removeStorageSync('currentDeviceItem')
})
</script>

<style lang="scss" scoped>
.container {
  height: $uni-height-area;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
  padding-top: 30rpx;
}

.device-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  border-radius: 8rpx;
}

.device-info {
  height: 125rpx;
  display: flex;
  align-items: center;
}

.device-icon {
  width: 125rpx;
  height: 125rpx;
  margin-right: 20rpx;
}

.device-details {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.device-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #000;
  display: flex;
  align-items: center;
}

.device-type {
  font-size: 24rpx;
  color: #2979ff;
  background-color: #e8f4ff;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.device-specs {
  display: flex;
}

.spec-item {
  font-size: 26rpx;
  color: #666;
  line-height: 36rpx;
  margin-right: 20rpx;
}

.form-container {
  flex: 1;
  margin-left: 30rpx;
  margin-right: 30rpx;
  border-radius: 8rpx;
}

.form-item {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  min-width: 200rpx;
  position: relative;

  &.required:before {
    content: '*';
    color: #ff4d4f;
    margin-right: 6rpx;
  }
}

.form-content {
  flex: 1;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: transparent;
  box-sizing: border-box;
  text-align: right;

  &:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }
}

.form-display-text {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  width: 100%;
  display: block;
}

.number-box-right {
  margin-left: auto;
  display: flex;
  justify-content: flex-end;
}

.bottom-bar {
  padding: 20rpx;
  background-color: #fff;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background-color: #2979ff;
  border-radius: 8rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
