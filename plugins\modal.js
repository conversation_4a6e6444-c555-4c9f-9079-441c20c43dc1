export default {
  // 消息提示
  msg(content) {
    uni.showToast({
      title: content,
      icon: 'none'
    })
  },
  // 错误消息
  msgError(content) {
    uni.showToast({
      title: content,
      icon: 'error'
    })
  },
  // 成功消息
  msgSuccess(content) {
    uni.showToast({
      title: content,
      icon: 'success'
    })
  },
  // 隐藏消息
  hideMsg(content) {
    uni.hideToast()
  },
  // 弹出提示
  alert(content, title) {
    uni.showModal({
      title: title || '系统提示',
      content: content,
      showCancel: false
    })
  },
  // 确认窗体
  confirm(content, title) {
    return new Promise((resolve, reject) => {
      uni.showModal({
        title: title || '系统提示',
        content: content,
        cancelText: '取消',
        confirmText: '确定',
        success: function(res) {
          if (res.confirm) {
            resolve(res.confirm)
          }
        }
      })
    })
  },
  // 提示信息
  showToast(option) {
    if (typeof option === "object") {
      uni.showToast(option)
    } else {
      uni.showToast({
        title: option,
        icon: "none",
        duration: 2500
      })
    }
  },
  // 打开遮罩层
  loading(content) {
    uni.showLoading({
      title: content,
      icon: 'none'
    })
  },
  // 关闭遮罩层
  closeLoading() {
    try {
        uni.hideLoading()
    } catch (e) {
        console.log(e)
    }
  },

  // 带输入框的确认弹窗 - 使用 uni-popup-dialog
  confirmWithInput(options = {}) {
    return new Promise((resolve, reject) => {
      const {
        title = '请输入',
        placeholder = '请输入内容',
        defaultValue = '',
        maxlength = 200,
        required = true
      } = options

      // 触发全局事件，使用 UniInputDialog 组件
      uni.$emit('showUniInputDialog', {
        title,
        placeholder,
        value: defaultValue,
        maxlength,
        onConfirm: (value) => {
          if (required && !value.trim()) {
            uni.showToast({
              title: '请输入内容',
              icon: 'none'
            })
            return
          }
          resolve(value)
        },
        onCancel: () => reject(new Error('用户取消'))
      })
    })
  },

  // 简化版本：使用 uni-popup-dialog 组件
  promptInput(title = '请输入', placeholder = '请输入内容', defaultValue = '') {
    return new Promise((resolve, reject) => {
      // 触发全局事件，让页面显示输入弹窗
      uni.$emit('showUniInputDialog', {
        title,
        placeholder,
        value: defaultValue,
        onConfirm: (value) => {
          resolve(value || defaultValue)
        },
        onCancel: () => {
          reject(new Error('用户取消'))
        }
      })
    })
  }
}
