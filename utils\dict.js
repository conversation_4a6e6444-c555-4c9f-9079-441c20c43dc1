import useDictStore from "@/store/modules/dict"
import { getDicts } from "@/api/system/dict/data"
import { ref, toRefs } from "vue"

/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({})
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        getDicts(dictType).then((resp) => {
          res.value[dictType] = resp.data.map((p) => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass, elTagClass: p.cssClass }))
          useDictStore().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}

export const typeList = {
  device_category: [
    {
      label: '摄像头',
      value: '0'
    },
    {
      label: '探头',
      value: '1'
    },
    {
      label: '配件',
      value: '2'
    },
    {
      label: '其他',
      value: '3'
    }
  ],
}
/** 获取自定义的类型数据 */
export function useTypeList(...args) {
  let res = {}
  return (() => {
    args.forEach((dictType, index) => {
      res[dictType] = typeList[dictType]
    })
    return res
  })()
}