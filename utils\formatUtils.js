/**
 * 格式化维保期限（秒转小时和分钟）
 * @param {Number} seconds 秒数
 * @returns {String} 格式化后的字符串
 */
export function formatDuration(seconds) {
  if (!seconds) return '0分钟'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0 && minutes > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return '0分钟'
  }
} 