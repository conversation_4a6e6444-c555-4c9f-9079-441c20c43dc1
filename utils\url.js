/**
 * URL 相关工具函数
 */

/**
 * 构建查询字符串
 * @param {Object} params - 参数对象
 * @param {boolean} filterEmpty - 是否过滤空值，默认为 false（保留所有参数）
 * @returns {string} 查询字符串
 */
export function buildQueryString(params, filterEmpty = false) {
  if (!params || typeof params !== 'object') {
    return ''
  }

  return Object.keys(params)
    .filter(key => {
      if (!filterEmpty) return true
      const value = params[key]
      return value !== '' && value !== null && value !== undefined
    })
    .map(key => {
      const value = params[key]
      // 将 undefined、null 等 falsy 值转换为空字符串
      const safeValue = (value === null || value === undefined) ? '' : value
      return `${encodeURIComponent(key)}=${encodeURIComponent(safeValue)}`
    })
    .join('&')
}

/**
 * 解析查询字符串
 * @param {string} queryString - 查询字符串
 * @returns {Object} 参数对象
 */
export function parseQueryString(queryString) {
  if (!queryString) return {}
  
  // 移除开头的 ? 号
  const query = queryString.startsWith('?') ? queryString.slice(1) : queryString
  
  const params = {}
  const pairs = query.split('&')
  
  pairs.forEach(pair => {
    const [key, value] = pair.split('=')
    if (key) {
      params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : ''
    }
  })
  
  return params
}

/**
 * 构建完整的导航 URL
 * @param {string} path - 页面路径
 * @param {Object} params - 参数对象
 * @returns {string} 完整的 URL
 */
export function buildNavigateUrl(path, params) {
  const queryString = buildQueryString(params)
  return queryString ? `${path}?${queryString}` : path
}

/**
 * 安全的页面导航（兼容不同环境）
 * @param {string} path - 页面路径
 * @param {Object} params - 参数对象
 * @param {Object} options - 导航选项
 */
export function safeNavigateTo(path, params = {}, options = {}) {
  const url = buildNavigateUrl(path, params)
  
  // 使用 uni.navigateTo 进行导航
  uni.navigateTo({
    url,
    ...options,
    success: (res) => {
      console.log('导航成功:', url)
      options.success && options.success(res)
    },
    fail: (err) => {
      console.error('导航失败:', err)
      options.fail && options.fail(err)
    }
  })
}

/**
 * 示例用法：
 *
 * // 构建查询字符串（保留所有参数，包括空值）
 * const queryStr = buildQueryString({
 *   deviceId: '123',
 *   pointId: '',
 *   categoryId: undefined,
 *   categoryName: '设备类型'
 * })
 * // 结果: "deviceId=123&pointId=&categoryId=&categoryName=%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B"
 *
 * // 构建查询字符串（过滤空值）
 * const queryStr2 = buildQueryString({
 *   deviceId: '123',
 *   pointId: '',
 *   categoryName: '设备类型'
 * }, true)
 * // 结果: "deviceId=123&categoryName=%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B"
 *
 * // 构建完整 URL
 * const url = buildNavigateUrl('/pages/deviceManagement/maintenanceRecords', {
 *   deviceId: '123',
 *   pointId: undefined,
 *   categoryId: ''
 * })
 * // 结果: "/pages/deviceManagement/maintenanceRecords?deviceId=123&pointId=&categoryId="
 *
 * // 安全导航
 * safeNavigateTo('/pages/deviceManagement/maintenanceRecords', {
 *   deviceId: '123',
 *   pointId: '456'
 * })
 */
